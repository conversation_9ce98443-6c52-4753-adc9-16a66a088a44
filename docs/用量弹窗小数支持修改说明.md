# 用量弹窗小数支持修改说明

## 需求描述
开方界面的用法用量部分，对于水丸、蜜丸、膏方这三种剂型，点击弹出"选择用量"弹窗，当前弹窗有自定义按钮的时候，点击自定义，弹出自定义用量弹窗。需要支持输入1位小数，比如0.5、1.2这种输入。

## 修改内容

### 1. 键盘类型修改
**文件**: `BRZY/Classes/Sections/BRPresUsageView.m`
**位置**: 第2678行
**修改**: 将键盘类型从 `UIKeyboardTypeNumberPad` 改为 `UIKeyboardTypeDecimalPad` 以支持小数点输入

```objc
// 修改前
inputTextField.keyboardType = UIKeyboardTypeNumberPad;

// 修改后  
inputTextField.keyboardType = UIKeyboardTypeDecimalPad;  // 支持小数点输入
```

### 2. 输入验证逻辑修改
**文件**: `BRZY/Classes/Sections/BRPresUsageView.m`
**位置**: 第2800-2808行
**修改**: 替换原有的整数验证逻辑，使用新的小数验证方法

```objc
// 修改前
// 限制输入长度为3位数字
if (inputText.length > 3) {
    textField.text = [inputText substringToIndex:3];
    inputText = textField.text;
}

// 只允许数字输入
NSCharacterSet *nonDigitSet = [[NSCharacterSet decimalDigitCharacterSet] invertedSet];
if ([inputText rangeOfCharacterFromSet:nonDigitSet].location != NSNotFound) {
    // 移除非数字字符
    textField.text = [[inputText componentsSeparatedByCharactersInSet:nonDigitSet] componentsJoinedByString:@""];
    inputText = textField.text;
}

// 修改后
// 验证小数格式并限制输入
NSString *validatedText = [self validateDecimalInput:inputText];
if (![validatedText isEqualToString:inputText]) {
    textField.text = validatedText;
    inputText = validatedText;
}
```

### 3. 重量计算逻辑修改
**文件**: `BRZY/Classes/Sections/BRPresUsageView.m`
**位置**: 第2819行
**修改**: 将整数计算改为小数计算

```objc
// 修改前
int unitCount = [inputText intValue];

// 修改后
double unitCount = [inputText doubleValue];  // 改为支持小数
```

### 4. 属性类型修改
**文件**: `BRZY/Classes/Sections/BRPresUsageView.h`
**修改**: 将相关属性从 `NSInteger` 改为 `double` 类型

```objc
// 修改前
@property (nonatomic, assign) NSInteger selectedUnitCount; // 蜜丸
@property (nonatomic, assign) NSInteger selectedWaterPillUnitCount; // 水丸
@property (nonatomic, assign) NSInteger selectedCreamFormulaUnitCount; // 膏方
@property (nonatomic, assign) NSInteger selectedCapsuleUnitCount; // 胶囊

// 修改后
@property (nonatomic, assign) double selectedUnitCount; // 支持小数
@property (nonatomic, assign) double selectedWaterPillUnitCount; // 支持小数
@property (nonatomic, assign) double selectedCreamFormulaUnitCount; // 支持小数
@property (nonatomic, assign) double selectedCapsuleUnitCount; // 支持小数
```

### 5. 显示更新方法修改
**文件**: `BRZY/Classes/Sections/BRPresUsageView.m`
**修改**: 更新各剂型的显示方法以支持小数显示

```objc
// 修改前（以蜜丸为例）
NSString *dosageText = [NSString stringWithFormat:@"%ld", (long)self.selectedUnitCount];

// 修改后
NSString *dosageText;
if (self.selectedUnitCount == (long)self.selectedUnitCount) {
    // 整数显示
    dosageText = [NSString stringWithFormat:@"%.0f", self.selectedUnitCount];
} else {
    // 小数显示，保留1位小数
    dosageText = [NSString stringWithFormat:@"%.1f", self.selectedUnitCount];
}
```

### 6. 保存按钮处理修改
**文件**: `BRZY/Classes/Sections/BRPresUsageView.m`
**位置**: 第2850-2873行
**修改**: 移除整数转换，直接使用小数值

```objc
// 修改前
self.selectedUnitCount = (NSInteger)inputValue;
self.selectedWaterPillUnitCount = (NSInteger)inputValue;
self.selectedCreamFormulaUnitCount = (NSInteger)inputValue;

// 修改后
self.selectedUnitCount = inputValue;  // 支持小数
self.selectedWaterPillUnitCount = inputValue;  // 支持小数
self.selectedCreamFormulaUnitCount = inputValue;  // 支持小数
```

### 7. 小数输入验证方法
**文件**: `BRZY/Classes/Sections/BRPresUsageView.m`
**位置**: 第2995-3048行
**新增**: 添加小数输入验证方法

```objc
/**
 * 验证小数输入，支持1位小数
 * @param inputText 输入的文本
 * @return 验证后的有效文本
 */
- (NSString *)validateDecimalInput:(NSString *)inputText {
    // 支持格式：整数和1位小数，如：1、12、1.5、12.3
    // 限制总长度不超过4位（例如：99.9）
    // 使用正则表达式验证：^\\d{1,2}(\\.\\d{0,1})?$
}
```

### 8. 其他相关修改
**文件**: `BRZY/Classes/Sections/PrescriptionViewController.m`
**修改**: 更新处方恢复逻辑，移除四舍五入，支持小数

```objc
// 修改前
_usageView.selectedUnitCount = (NSInteger)round(packageCount);

// 修改后
_usageView.selectedUnitCount = packageCount;  // 支持小数，不再四舍五入
```

## 影响范围
- 水丸剂型的自定义用量输入
- 蜜丸剂型的自定义用量输入  
- 膏方剂型的自定义用量输入
- 胶囊剂型的自定义用量输入（为保持一致性也进行了修改）

## 测试建议
1. 测试水丸、蜜丸、膏方剂型的用量选择弹窗
2. 点击"自定义"按钮，测试小数输入（如0.5、1.2、2.8等）
3. 测试边界情况（如99.9、0.1等）
4. 测试无效输入的处理（如多个小数点、超长输入等）
5. 测试整数输入仍然正常工作（如1、2、3等）

## 注意事项
- 小数输入限制为1位小数
- 总长度限制为4位字符（如99.9）
- 整数部分最多2位数字
- 保持了原有的样式和交互逻辑不变
