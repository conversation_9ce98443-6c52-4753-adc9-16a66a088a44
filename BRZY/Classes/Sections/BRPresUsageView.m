//
//  BRPresUsageView.m
//  BRZY
//  开处方用法用量
//  Created by e<PERSON><PERSON> on 2017/10/13.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BRPresUsageView.h"

#import "BRPrescriptionTitleView.h"
#import "MMNumberKeyboard.h"
#import "BRActionSheetView.h"
#import <objc/runtime.h>

// 定义行高度常量
#define ROW_HEIGHT 50.0f
#define TIME_ROW_HEIGHT 50.0f

@interface BRPresUsageView()<UITextFieldDelegate,MMNumberKeyboardDelegate>


@property (nonatomic, strong) BRPrescriptionTitleView *titleView;
@property (nonatomic, strong) UIButton *changeButton;
@property (nonatomic, strong) UIView *bgView;

//用药时间
@property (nonatomic, strong) UILabel *before_time_label;

//用药时间底部线条
@property (nonatomic, strong) UIView *usageBottomLineView;

@end

@implementation BRPresUsageView

#pragma mark - 行容器创建方法

// 创建一个行容器视图
- (UIView *)createRowContainerWithHeight:(CGFloat)height {
    UIView *rowContainer = [[UIView alloc] init];
    rowContainer.backgroundColor = [UIColor whiteColor];
    rowContainer.clipsToBounds = YES; // 设置裁剪边界，隐藏超出容器的内容
    [self addSubview:rowContainer];
    [self.rowContainers addObject:rowContainer];
    
    // 创建底部分割线
    UIView *bottomLine = [[UIView alloc] init];
    bottomLine.backgroundColor = [UIColor clearColor]; // 设置透明背景
    [rowContainer addSubview:bottomLine];
    [bottomLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(rowContainer);
        make.bottom.equalTo(rowContainer).offset(-1); // 上移1像素
        make.height.mas_equalTo(0.5);
    }];
    
    // 标记底部线条，稍后统一绘制
    bottomLine.tag = 999; // 标记为底部虚线
    
    return rowContainer;
}

// 清空所有行容器
- (void)clearAllRowContainers {
    for (UIView *container in self.rowContainers) {
        [container removeFromSuperview];
    }
    [self.rowContainers removeAllObjects];
}

// 布局行容器（从上到下依次排列）
- (void)layoutRowContainers {
    UIView *previousContainer = self.titleView;
    
    // 收集所有可见的容器
    NSMutableArray<UIView *> *visibleContainers = [NSMutableArray array];
    for (UIView *container in self.rowContainers) {
        if (!container.hidden) {
            [visibleContainers addObject:container];
        }
    }
    
    // 为所有容器设置约束
    for (NSInteger i = 0; i < self.rowContainers.count; i++) {
        UIView *container = self.rowContainers[i];
        
        CGFloat height;
        if (container.hidden) {
            height = 0; // 隐藏的容器高度设为0
        } else {
            // 判断是否是最后一个可见容器
            BOOL isLastVisible = (container == visibleContainers.lastObject);
            height = isLastVisible ? TIME_ROW_HEIGHT : ROW_HEIGHT;
        }
        
        [container mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self);
            make.top.equalTo(previousContainer.mas_bottom);
            make.height.mas_equalTo(height);
        }];
        previousContainer = container;
    }
    
    // 布局完成后更新整体高度
    [self updateTotalHeight];
    
    // 强制布局后绘制虚线
    [self layoutIfNeeded];
    dispatch_async(dispatch_get_main_queue(), ^{
        [self drawAllDashLines];
    });
}

// 更新整体高度并通知回调
- (void)updateTotalHeight {
    if (!self.changeUsageHeight) {
        return;
    }
    
    // 计算总高度：标题高度 + 所有可见行的高度
    CGFloat totalHeight = kTitleViewHeight;
    NSInteger visibleRowCount = 0;
    NSInteger lastVisibleRowIndex = -1;
    
    // 先统计可见行
    for (NSInteger i = 0; i < self.rowContainers.count; i++) {
        UIView *container = self.rowContainers[i];
        if (!container.hidden) {
            visibleRowCount++;
            lastVisibleRowIndex = i;
        }
    }
    
    // 计算每个可见行的高度
    NSInteger currentVisibleIndex = 0;
    for (NSInteger i = 0; i < self.rowContainers.count; i++) {
        UIView *container = self.rowContainers[i];
        if (!container.hidden) {
            // 最后一个可见行使用TIME_ROW_HEIGHT，其他使用ROW_HEIGHT
            if (i == lastVisibleRowIndex) {
                totalHeight += TIME_ROW_HEIGHT;
            } else {
                totalHeight += ROW_HEIGHT;
            }
            currentVisibleIndex++;
        }
    }
    
    // 如果是散剂类型，需要额外添加50高度
    if ([self.drugForm isEqualToString:@"散剂"]) {
        totalHeight += 50;
    }
    
    
    // 通知父视图高度变化
    self.changeUsageHeight(totalHeight);
}

// 绘制所有虚线分割线
- (void)drawAllDashLines {
    for (NSInteger i = 0; i < self.rowContainers.count; i++) {
        UIView *container = self.rowContainers[i];
        UIView *bottomLine = [container viewWithTag:999];
        
        // 如果容器被隐藏，不绘制虚线
        if (container.hidden) {
            if (bottomLine) {
                bottomLine.hidden = YES;
            }
            continue;
        }
        
        // 判断是否是最后一个可见行
        BOOL isLastVisibleRow = YES;
        for (NSInteger j = i + 1; j < self.rowContainers.count; j++) {
            if (!self.rowContainers[j].hidden) {
                isLastVisibleRow = NO;
                break;
            }
        }
        
        // 判断最后一行是否需要显示分割线
        BOOL shouldShowLastRowLine = [self shouldShowLastRowDividerLine];
        
        if (bottomLine && (!isLastVisibleRow || shouldShowLastRowLine)) {
            bottomLine.hidden = NO; // 确保显示
            [ViewTools drawDashLine:bottomLine lineLength:5 lineSpacing:4 lineColor:[UIColor br_insideDivisionLineColor]];
        } else if (bottomLine && isLastVisibleRow && !shouldShowLastRowLine) {
            // 最后一个可见行不需要分割线时隐藏
            bottomLine.hidden = YES;
        }
    }
}

// 判断最后一行是否需要显示分割线
- (BOOL)shouldShowLastRowDividerLine {
    // 饮片、代煎、散剂需要在时间行下方显示分割线，因为下面还有用药方法区域
    return [self.drugForm isEqualToString:@"饮片"] || 
           [self.drugForm isEqualToString:@"代煎"] || 
           [self.drugForm isEqualToString:@"散剂"];
}

// 此方法已移动到文件末尾，这里删除重复声明

// 重写layoutSubviews确保虚线在布局后正确绘制
- (void)layoutSubviews {
    [super layoutSubviews];
    
    // 在布局完成后重新绘制虚线（使用延迟确保frame已更新）
    dispatch_async(dispatch_get_main_queue(), ^{
        [self drawAllDashLines];
    });
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        
        self.backgroundColor = [UIColor whiteColor];
        
        _drugType = BRDrugTypeKAndY;
        
        // 初始化行容器数组
        _rowContainers = [[NSMutableArray alloc] init];
        
        // 初始化辅料相关属性
        _selectedAuxiliaryMaterials = [[NSMutableArray alloc] init];
        _isNoAuxiliaryMaterial = NO;
        
        _titleView = [[BRPrescriptionTitleView alloc]initWithTitle:@"用法用量"];
        [self addSubview:_titleView];
        
        [_titleView mas_makeConstraints:^(MASConstraintMaker *make) {
            
            make.top.equalTo(self);
            make.left.and.right.equalTo(self);
            make.height.mas_equalTo(kTitleViewHeight);
            
        }];
        
        UIView *middleLine = [[UIView alloc]init];
        middleLine.frame = CGRectMake(0, 95, SCREEN_WIDTH, .5);
        [self addSubview:middleLine];
        
        
        UIView *usageBottomLineView = [[UIView alloc] init];
        usageBottomLineView.frame = CGRectMake(0, 95, SCREEN_WIDTH, 0.5);
        [self addSubview:usageBottomLineView];
        
        self.usageBottomLineView = usageBottomLineView;
        
        usageBottomLineView.hidden = YES;
        
        
        [ViewTools drawDashLine:middleLine lineLength:5 lineSpacing:4 lineColor:[UIColor br_insideDivisionLineColor]];
        
        [ViewTools drawDashLine:usageBottomLineView lineLength:5 lineSpacing:4 lineColor:[UIColor br_insideDivisionLineColor]];
        
        _bgView = [[UIView alloc]init];
        _bgView.backgroundColor = [UIColor whiteColor];
        [self addSubview:_bgView];
        
        [_bgView mas_makeConstraints:^(MASConstraintMaker *make) {
            
            make.top.equalTo(self.titleView.mas_bottom);
            make.left.and.right.equalTo(self);
            make.height.mas_equalTo(50);
            
        }];
        
        //创建颗粒或者饮片的view
        [self buildYAndKTypeView];
        
        // 注意：用药时间控件现在在setupTimeRowInContainer方法中创建，避免重复
        
        // 初始化默认值
//        _selectedMode = @"内服";
        
        // 创建用药方法相关控件
        _usageMethodLabel = [[UILabel alloc] init];
        _usageMethodLabel.text = @"用药方法";
        _usageMethodLabel.font = kFontLight(16);
        _usageMethodLabel.textColor = [UIColor br_textBlackColor];
        _usageMethodLabel.hidden = YES;
        [self addSubview:_usageMethodLabel];
        
        _internalUseButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_internalUseButton setTitle:@"内服" forState:UIControlStateNormal];
        [_internalUseButton setTitleColor:[UIColor br_textBlackColor] forState:UIControlStateNormal];
        [_internalUseButton setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
        [_internalUseButton addTarget:self action:@selector(usageMethodButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
        _internalUseButton.layer.borderWidth = 0.5;
        _internalUseButton.layer.cornerRadius = 4;
        _internalUseButton.hidden = YES;
//        _internalUseButton.selected = YES;
        [self addSubview:_internalUseButton];
        
        _externalUseButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_externalUseButton setTitle:@"外用" forState:UIControlStateNormal];
        [_externalUseButton setTitleColor:[UIColor br_textBlackColor] forState:UIControlStateNormal];
        [_externalUseButton setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
        [_externalUseButton addTarget:self action:@selector(usageMethodButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
        _externalUseButton.layer.borderWidth = 0.5;
        _externalUseButton.layer.cornerRadius = 4;
        _externalUseButton.hidden = YES;
        [self addSubview:_externalUseButton];
        
        // 添加约束
        [_usageMethodLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self).offset(kHorizontalMargin);
            make.bottom.equalTo(self).offset(-15);
            make.width.mas_equalTo(80);
            make.height.mas_equalTo(20);
        }];
        
        [_externalUseButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.mas_equalTo(@-20);
            make.centerY.equalTo(_usageMethodLabel);
            make.width.mas_equalTo(60);
            make.height.mas_equalTo(30);
        }];
        
        [_internalUseButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.externalUseButton.mas_left).with.offset(-20);
            make.centerY.equalTo(_usageMethodLabel);
            make.width.mas_equalTo(60);
            make.height.mas_equalTo(30);
        }];
        
        [_usageBottomLineView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self).offset(-70); // 使用固定位置替代动态引用
            make.left.right.equalTo(self);
            make.height.mas_equalTo(0.5);
        }];
    }
    return self;
}


// 添加按钮点击事件处理
- (void)usageMethodButtonTapped:(UIButton *)sender {
//    if (sender == _internalUseButton) {
//        _internalUseButton.selected = YES;
//        _externalUseButton.selected = NO;
//        _selectedMode = @"内服";
//        [_internalUseButton setBackgroundColor:[UIColor br_mainBlueColor]];
//        [_externalUseButton setBackgroundColor:[UIColor whiteColor]];
//    } else {
//        _internalUseButton.selected = NO;
//        _externalUseButton.selected = YES;
//        _selectedMode = @"外用";
//        [_internalUseButton setBackgroundColor:[UIColor whiteColor]];
//        [_externalUseButton setBackgroundColor:[UIColor br_mainBlueColor]];
//    }
    if (sender == _internalUseButton) {
           _selectedMode = @"内服";
       } else {
           _selectedMode = @"外用";
       }
       [Utils saveUsageType:kUsageMethodKey text:_selectedMode withPatientId:self.patientId];
       
       // 更新UI
       _internalUseButton.selected = [_selectedMode isEqualToString:@"内服"];
       _externalUseButton.selected = [_selectedMode isEqualToString:@"外用"];
       [_internalUseButton setBackgroundColor:_internalUseButton.selected ? [UIColor br_mainBlueColor] : [UIColor whiteColor]];
       [_externalUseButton setBackgroundColor:_externalUseButton.selected ? [UIColor br_mainBlueColor] : [UIColor whiteColor]];
}


// 胶囊颗数设置方法已移除

#pragma mark -
- (void)pressToChangeTime {
    
    _changeTime();
    
}

- (void)setDrugForm:(NSString *)drugForm {
    _drugForm = drugForm;
    
    // 1. 确定是否需要显示用药方法
    BOOL shouldShowUsageMethod = [self shouldShowUsageMethodForDrugForm:drugForm];
    [self updateUsageMethodVisibility:shouldShowUsageMethod];
    
    // 2. 调整视图高度
    [self updateViewHeightForDrugForm:drugForm shouldShowUsageMethod:shouldShowUsageMethod];
    
    // 3. 更新用药方式选择状态
    [self updateUsageModeSelection:shouldShowUsageMethod];
    
    // 4. 根据药品类型构建相应的视图
    if ([self isOtherTypeDrug:drugForm]) {
        [self buildAndRestoreOtherTypeView];
    } else {
        [self buildAndRestoreKAndYTypeView];
    }
    
    // 5. 特定剂型隐藏克数显示
    if ([drugForm isEqualToString:@"水丸"] || 
        [drugForm isEqualToString:@"蜜丸"] || 
        [drugForm isEqualToString:@"膏方"] || 
        [drugForm isEqualToString:@"胶囊"] || 
        [drugForm isEqualToString:@"散剂"]) {
        if (self.preDoseGramLabel) {
            self.preDoseGramLabel.hidden = YES;
            NSLog(@"剂型%@隐藏克数显示", drugForm);
        }
    }
}

#pragma mark - Helper Methods

- (BOOL)shouldShowUsageMethodForDrugForm:(NSString *)drugForm {
    return [drugForm isEqualToString:@"饮片"] ||
           [drugForm isEqualToString:@"代煎"] ||
           [drugForm isEqualToString:@"散剂"];
}

- (void)updateUsageMethodVisibility:(BOOL)shouldShow {
    _usageMethodLabel.hidden = !shouldShow;
    _internalUseButton.hidden = !shouldShow;
    _externalUseButton.hidden = !shouldShow;
}

- (void)updateViewHeightForDrugForm:(NSString *)drugForm shouldShowUsageMethod:(BOOL)shouldShow {
    // 高度计算现在在updateTotalHeight方法中统一处理
    // 这里只需要更新用药方法的显示状态
}

- (void)updateUsageModeSelection:(BOOL)shouldShow {
    if (!shouldShow) {
        _selectedMode = @"";
        return;
    }
    
    //    _internalUseButton.selected = YES;
    //    _externalUseButton.selected = NO;
    //    _selectedMode = @"内服";
    //
    //    [_internalUseButton setBackgroundColor:[UIColor br_mainBlueColor]];
    //    [_externalUseButton setBackgroundColor:[UIColor whiteColor]];
    
    // 读取保存的用药方法
    NSString *savedMethod = [Utils getUsageType:kUsageMethodKey withPatientId:self.patientId];
    if (![savedMethod isEqualToString:@"-1"] && ![savedMethod isEqualToString:@""]) {
        // 恢复保存的状态
        _selectedMode = savedMethod;
        BOOL isInternal = [savedMethod isEqualToString:@"内服"];
        _internalUseButton.selected = isInternal;
        _externalUseButton.selected = !isInternal;
        [_internalUseButton setBackgroundColor:isInternal ? [UIColor br_mainBlueColor] : [UIColor whiteColor]];
        [_externalUseButton setBackgroundColor:isInternal ? [UIColor whiteColor] : [UIColor br_mainBlueColor]];
    } else {
        
        
        _internalUseButton.selected = NO;
        _externalUseButton.selected = NO;
        [_internalUseButton setBackgroundColor:[UIColor whiteColor]];
        [_externalUseButton setBackgroundColor:[UIColor whiteColor]];
        
        // 默认状态
//        _internalUseButton.selected = YES;
//        _externalUseButton.selected = NO;
//        _selectedMode = @"内服";
//        [_internalUseButton setBackgroundColor:[UIColor br_mainBlueColor]];
//        [_externalUseButton setBackgroundColor:[UIColor whiteColor]];
    }
    
}

- (BOOL)isOtherTypeDrug:(NSString *)drugForm {
    return ![drugForm isEqualToString:@"颗粒"] &&
           ![drugForm isEqualToString:@"饮片"] &&
           ![drugForm isEqualToString:@"代煎"] &&
           ![drugForm isEqualToString:@"外用中药"];
}

- (void)buildAndRestoreOtherTypeView {
    [self buildOtherTypeViewWithDrugForm:self.drugForm];
    
    NSString *drugFormMd5 = [[self.drugForm md5String] substringToIndex:5];
    
    // 散剂特殊处理（显示/隐藏底部线条）
    if ([self.drugForm isEqualToString:@"散剂"]) {
        self.usageBottomLineView.hidden = NO;
    } else {
        self.usageBottomLineView.hidden = YES;
    }
    
    // 构建键名
    NSString *otherTypeEveryDayTimes = [NSString stringWithFormat:@"%@_%@",
        kUsageOtherType_EveryDayTimes, drugFormMd5];
    NSString *otherTypeEveryTimesAmount = [NSString stringWithFormat:@"%@_%@",
        kUsageOtherType_EveryTimesAmount, drugFormMd5];
    NSString *otherTypeEstimateDays = [NSString stringWithFormat:@"%@_%@",
        kUsageOtherType_EstimateDays, drugFormMd5];
    
    // 处理快速开方情况
    if (![self.quickOrderTempId isEqualToString:@""]) {
        otherTypeEveryDayTimes = [NSString stringWithFormat:@"%@_%@",
            otherTypeEveryDayTimes, self.quickOrderTempId];
        otherTypeEveryTimesAmount = [NSString stringWithFormat:@"%@_%@",
            otherTypeEveryTimesAmount, self.quickOrderTempId];
        otherTypeEstimateDays = [NSString stringWithFormat:@"%@_%@",
            otherTypeEstimateDays, self.quickOrderTempId];
    }
    
    // 恢复上次修改的内容
    [self restoreOtherTypeValuesWithKeys:@{
        otherTypeEveryDayTimes: _timesTextField,
        otherTypeEveryTimesAmount: _preDoseTextField,
        otherTypeEstimateDays: _dayTextField
    }];
}

- (void)buildAndRestoreKAndYTypeView {
    [self buildYAndKTypeView];
    
    //如果为饮片和代煎，显示用药方法底部线条
    if ([self.drugForm isEqualToString:@"饮片"] || [self.drugForm isEqualToString:@"代煎"]) {
        self.usageBottomLineView.hidden = NO;
    } else {
        self.usageBottomLineView.hidden = YES;
    }
    
    // 构建键名
    NSString *kandYTypeAll = @"kUsageKandYType_All";
    NSString *kandYTypeEveryDayAmount = @"kUsageKandYType_EveryDayAmount";
    NSString *kandYTypeTimes = @"kUsageKandYType_times";
    
    // 处理快速开方情况
    if (![self.quickOrderTempId isEqualToString:@""]) {
        kandYTypeAll = [NSString stringWithFormat:@"%@_%@",
            kandYTypeAll, self.quickOrderTempId];
        kandYTypeEveryDayAmount = [NSString stringWithFormat:@"%@_%@",
            kandYTypeEveryDayAmount, self.quickOrderTempId];
        kandYTypeTimes = [NSString stringWithFormat:@"%@_%@",
            kandYTypeTimes, self.quickOrderTempId];
    }
    
    // 恢复上次修改的内容
    [self restoreKAndYTypeValuesWithKeys:@{
        kandYTypeAll: _drugNumTextField,
        kandYTypeEveryDayAmount: _usageTextField,
        kandYTypeTimes: _timesTextField
    }];
}

- (void)restoreOtherTypeValuesWithKeys:(NSDictionary<NSString *, UITextField *> *)keyFieldMap {
    [keyFieldMap enumerateKeysAndObjectsUsingBlock:^(NSString *key, UITextField *textField, BOOL *stop) {
        NSString *value = [Utils getUsageType:key withPatientId:self.patientId];
        if (![value isEqualToString:@"-1"] && ![value isEqualToString:@""]) {
            textField.text = value;
        }
    }];
}

- (void)restoreKAndYTypeValuesWithKeys:(NSDictionary<NSString *, UITextField *> *)keyFieldMap {
    [keyFieldMap enumerateKeysAndObjectsUsingBlock:^(NSString *key, UITextField *textField, BOOL *stop) {
        NSString *value = [Utils getUsageType:key withPatientId:self.patientId];
        if (![value isEqualToString:@"-1"] && ![value isEqualToString:@""]) {
            textField.text = value;
        }
    }];
}

- (void)setTotalDoseStr:(NSString *)totalDoseStr {
    
    _totalDoseStr = totalDoseStr;
    _totalDoseLabel.text = totalDoseStr;
    
    [_totalDoseLabel sizeToFit];
    
    [self layoutIfNeeded];
    
    CGFloat height = _totalDoseLabel.size.height;
    
    [_totalDoseLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(height);
    }];
    
    // 注意：不再更新_bgView高度，使用新的行容器系统
    // 重新计算总高度
    [self updateTotalHeight];
    
    // 散剂的特殊处理（显示/隐藏底部线条）
    if ([self.drugForm isEqualToString:@"散剂"]) {
        self.usageBottomLineView.hidden = NO;
    }else{
        self.usageBottomLineView.hidden = YES;
    }
}

- (void)buildOtherTypeViewWithDrugForm:(NSString *)drugForm {
    
    // 清空现有的行容器
    [self clearAllRowContainers];
    
    // 判断是否需要显示规格行和辅料行
    BOOL needsSpecificationRow = [self shouldShowSpecificationRowForDrugForm:drugForm];
    BOOL needsAuxiliaryMaterialRow = [self shouldShowAuxiliaryMaterialRowForDrugForm:drugForm];
    
    // 创建行容器
    UIView *totalDoseRow = [self createRowContainerWithHeight:ROW_HEIGHT];    // 预计总重
    UIView *specificationRow = nil;
    if (needsSpecificationRow) {
        specificationRow = [self createRowContainerWithHeight:ROW_HEIGHT];    // 规格行
    }
    UIView *auxiliaryMaterialRow = nil;
    if (needsAuxiliaryMaterialRow) {
        auxiliaryMaterialRow = [self createRowContainerWithHeight:ROW_HEIGHT]; // 辅料行
    }
    UIView *usageRow = [self createRowContainerWithHeight:ROW_HEIGHT];        // 每日X次 每次Xg 预计服用X天（水平排列在同一行）
    UIView *timeRow = [self createRowContainerWithHeight:TIME_ROW_HEIGHT];    // 用药时间
    
    // 第1行：预计总重
    [self setupTotalDoseRowInContainer:totalDoseRow];
    
    // 第2行：规格（仅特定剂型显示）
    if (needsSpecificationRow && specificationRow) {
        [self setupSpecificationRowInContainer:specificationRow];
    }
    
    // 第3行：辅料（仅膏方剂型且有辅料数据时显示）
    if (needsAuxiliaryMaterialRow && auxiliaryMaterialRow) {
        [self setupAuxiliaryMaterialRowInContainer:auxiliaryMaterialRow];
    }
    
    // 第4行：每日XX次 + 每次XXg + 预计服用XX天（水平排列在同一行）
    [self setupOtherUsageRowInContainer:usageRow drugForm:drugForm];
    
    // 第5行：用药时间
    [self setupTimeRowInContainer:timeRow];
    
    // 在所有内容设置完成后，再布局行容器
    [self layoutRowContainers];
}

- (void)buildYAndKTypeView {
    
    // 清空现有的行容器
    [self clearAllRowContainers];
    
    // 创建行容器 - 颗粒/饮片/代煎只需要2行：用法用量行 + 用药时间行
    UIView *usageRow = [self createRowContainerWithHeight:ROW_HEIGHT];       // 共X剂 每日X剂 每剂分X次服用（一行）
    UIView *timeRow = [self createRowContainerWithHeight:TIME_ROW_HEIGHT];   // 用药时间
    
    // 第1行：共XX剂 + 每日XX剂 + 每剂分XX次服用（水平排列在同一行）
    [self setupKandYUsageRowInContainer:usageRow];
    
    // 第2行：用药时间
    [self setupTimeRowInContainer:timeRow];
    
    // 在所有内容设置完成后，再布局行容器
    [self layoutRowContainers];
}

#pragma mark - 颗粒/饮片/代煎 行设置方法

// 设置颗粒/饮片/代煎用法用量行（共X剂 每日X剂 每剂分X次服用 - 水平排列在同一行）
- (void)setupKandYUsageRowInContainer:(UIView *)container {
    __weak __typeof(self)weakSelf = self;
    
    // 第一部分：共XX剂
    UILabel *before_drugNum_label = [[UILabel alloc]init];
    before_drugNum_label.text = @"共";
    before_drugNum_label.font = kFontLight(16);
    before_drugNum_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:before_drugNum_label];
    
    MMNumberKeyboard *drugNumkeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    drugNumkeyboard.allowsDecimalPoint = NO;
    drugNumkeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    drugNumkeyboard.delegate = self;
    _drugNumTextField = [[BRUnderlineRedTextField alloc]init];
    _drugNumTextField.text = @"7";
    _drugNumTextField.delegate = self;
    _drugNumTextField.inputView = drugNumkeyboard;
    [container addSubview:_drugNumTextField];
    [_drugNumTextField addTarget:self action:@selector(textFieldEditChanged:) forControlEvents:UIControlEventEditingChanged];
    
    UILabel *behind_drugNum_label = [[UILabel alloc]init];
    behind_drugNum_label.text = @"剂";
    behind_drugNum_label.font = kFontLight(16);
    behind_drugNum_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:behind_drugNum_label];
    
    // 第二部分：每日XX剂
    UILabel *before_usage_label = [[UILabel alloc]init];
    before_usage_label.text = @"每日";
    before_usage_label.font = kFontLight(16);
    before_usage_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:before_usage_label];
    
    MMNumberKeyboard *usagekeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    usagekeyboard.allowsDecimalPoint = NO;
    usagekeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    usagekeyboard.delegate = self;
    _usageTextField = [[BRUnderlineRedTextField alloc]init];
    _usageTextField.text = @"1";
    _usageTextField.delegate = self;
    _usageTextField.inputView = usagekeyboard;
    [container addSubview:_usageTextField];
    
    UILabel *behind_usage_label = [[UILabel alloc]init];
    behind_usage_label.text = @"剂";
    behind_usage_label.font = kFontLight(16);
    behind_usage_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:behind_usage_label];
    
    // 第三部分：每剂分XX次服用
    UILabel *before_times_label = [[UILabel alloc]init];
    before_times_label.text = @"每剂分";
    before_times_label.font = kFontLight(16);
    before_times_label.textAlignment = NSTextAlignmentRight;
    before_times_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:before_times_label];
    
    MMNumberKeyboard *timeskeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    timeskeyboard.allowsDecimalPoint = NO;
    timeskeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    timeskeyboard.delegate = self;
    _timesTextField = [[BRUnderlineRedTextField alloc]init];
    _timesTextField.font = kFontLight(16);
    _timesTextField.delegate = self;
    _timesTextField.text = @"2";  // 颗粒、饮片、代煎和外用中药等普通剂型默认为2次
    _timesTextField.inputView = timeskeyboard;
    [container addSubview:_timesTextField];
    
    // 为 timesTextField 添加点击事件处理，在代煎剂型下跳出选择器
    [_timesTextField addTarget:self action:@selector(timesTextFieldTapped:) forControlEvents:UIControlEventTouchDown];
    [_timesTextField addTarget:self action:@selector(timesTextFieldTapped:) forControlEvents:UIControlEventTouchUpInside];
    
    UILabel *behind_times_label = [[UILabel alloc]init];
    behind_times_label.text = @"次服用";
    behind_times_label.font = kFontLight(16);
    behind_times_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:behind_times_label];
    
    // 约束布局 - 水平排列
    [before_drugNum_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(container).offset(kHorizontalMargin);
        make.width.mas_equalTo(20);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(container);
    }];
    
    [_drugNumTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(before_drugNum_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_drugNum_label);
    }];
    
    [behind_drugNum_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_drugNumTextField.mas_right);
        make.height.and.width.mas_equalTo(20);
        make.centerY.equalTo(before_drugNum_label);
    }];
    
    [before_usage_label mas_makeConstraints:^(MASConstraintMaker *make) {
        if (isiPhone5) {
            make.left.equalTo(behind_drugNum_label.mas_right).offset(5);
        }
        else {
            make.left.equalTo(behind_drugNum_label.mas_right).offset(10);
        }
        make.width.mas_equalTo(40);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(container);
    }];
    
    [_usageTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(before_usage_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_usage_label);
    }];
    
    [behind_usage_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_usageTextField.mas_right);
        make.height.and.width.mas_equalTo(20);
        make.centerY.equalTo(before_usage_label);
    }];
    
    [before_times_label mas_makeConstraints:^(MASConstraintMaker *make) {
        if (isiPhone5) {
            make.left.equalTo(behind_usage_label.mas_right).offset(5);
            make.width.mas_equalTo(50);
        }
        else {
            make.left.equalTo(behind_usage_label.mas_right).offset(10);
            make.width.mas_equalTo(60);
        }
        make.height.mas_equalTo(20);
        make.centerY.equalTo(before_usage_label);
    }];
    
    [_timesTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(before_times_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_usage_label);
    }];
    
    [behind_times_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_timesTextField.mas_right);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(before_usage_label);
        make.right.lessThanOrEqualTo(container).offset(-kHorizontalMargin); // 确保不超出容器边界
    }];
    
    // 添加数据保存逻辑
    [[_drugNumTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *text = [_drugNumTextField text];
        NSString *type = [NSString stringWithFormat:@"%@",kUsageKandYType_All];
        //如果为快速开方
        if(weakSelf.quickOrderTempId.length > 0){
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
    
    [[_usageTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *type = [NSString stringWithFormat:@"%@",kUsageKandYType_EveryDayAmount];
        //如果为快速开方
        if(weakSelf.quickOrderTempId.length > 0){
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        NSString *text = [_usageTextField text];
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
    
    [[_timesTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *type = [NSString stringWithFormat:@"%@",kUsageKandYType_times];
        //如果为快速开方
        if(weakSelf.quickOrderTempId.length > 0){
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        NSString *text = [_timesTextField text];
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
}

// 设置共XX剂行
- (void)setupDrugNumRowInContainer:(UIView *)container {
    UILabel *before_drugNum_label = [[UILabel alloc]init];
    before_drugNum_label.text = @"共";
    before_drugNum_label.font = kFontLight(16);
    before_drugNum_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:before_drugNum_label];
    
    MMNumberKeyboard *drugNumkeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    drugNumkeyboard.allowsDecimalPoint = NO;
    drugNumkeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    drugNumkeyboard.delegate = self;
    _drugNumTextField = [[BRUnderlineRedTextField alloc]init];
    _drugNumTextField.text = @"7";
    _drugNumTextField.delegate = self;
    _drugNumTextField.inputView = drugNumkeyboard;
    [container addSubview:_drugNumTextField];
    
    UILabel *behind_drugNum_label = [[UILabel alloc]init];
    behind_drugNum_label.text = @"剂";
    behind_drugNum_label.font = kFontLight(16);
    behind_drugNum_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:behind_drugNum_label];
    
    // 布局
    [before_drugNum_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(container).offset(kHorizontalMargin);
        make.width.mas_equalTo(20);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(container);
    }];
    
    [_drugNumTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(before_drugNum_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_drugNum_label);
    }];
    
    [behind_drugNum_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.drugNumTextField.mas_right);
        make.height.and.width.mas_equalTo(20);
        make.centerY.equalTo(before_drugNum_label);
    }];
    
    // 添加事件监听
    [_drugNumTextField addTarget:self action:@selector(textFieldEditChanged:) forControlEvents:UIControlEventEditingChanged];
    
    __weak __typeof(self)weakSelf = self;
    [[_drugNumTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *text = [_drugNumTextField text];
        NSString *type = [NSString stringWithFormat:@"%@",kUsageKandYType_All];
        if(weakSelf.quickOrderTempId.length > 0){
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
}

// 设置每日XX剂行
- (void)setupUsageRowInContainer:(UIView *)container {
    UILabel *before_usage_label = [[UILabel alloc]init];
    before_usage_label.text = @"每日";
    before_usage_label.font = kFontLight(16);
    before_usage_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:before_usage_label];
    
    MMNumberKeyboard *usagekeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    usagekeyboard.allowsDecimalPoint = NO;
    usagekeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    usagekeyboard.delegate = self;
    _usageTextField = [[BRUnderlineRedTextField alloc]init];
    _usageTextField.text = @"1";
    _usageTextField.delegate = self;
    _usageTextField.inputView = usagekeyboard;
    [container addSubview:_usageTextField];
    
    UILabel *behind_usage_label = [[UILabel alloc]init];
    behind_usage_label.text = @"剂";
    behind_usage_label.font = kFontLight(16);
    behind_usage_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:behind_usage_label];
    
    // 布局
    [before_usage_label mas_makeConstraints:^(MASConstraintMaker *make) {
        if (isiPhone5) {
            make.left.equalTo(container).offset(kHorizontalMargin+90);
        } else {
            make.left.equalTo(container).offset(kHorizontalMargin+100);
        }
        make.width.mas_equalTo(40);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(container);
    }];
    
    [_usageTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(before_usage_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_usage_label);
    }];
    
    [behind_usage_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.usageTextField.mas_right);
        make.height.and.width.mas_equalTo(20);
        make.centerY.equalTo(before_usage_label);
    }];
    
    // 添加事件监听
    __weak __typeof(self)weakSelf = self;
    [[_usageTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *text = [_usageTextField text];
        NSString *type = [NSString stringWithFormat:@"%@",kUsageKandYType_EveryDayAmount];
        if(weakSelf.quickOrderTempId.length > 0){
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
}

// 设置每剂分XX次服用行
- (void)setupTimesRowInContainer:(UIView *)container {
    UILabel *before_times_label = [[UILabel alloc]init];
    before_times_label.text = @"每剂分";
    before_times_label.font = kFontLight(16);
    before_times_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:before_times_label];
    
    MMNumberKeyboard *timeskeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    timeskeyboard.allowsDecimalPoint = NO;
    timeskeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    timeskeyboard.delegate = self;
    _timesTextField = [[BRUnderlineRedTextField alloc]init];
    _timesTextField.text = @"2";
    _timesTextField.delegate = self;
    _timesTextField.inputView = timeskeyboard;
    [container addSubview:_timesTextField];
    
    UILabel *behind_times_label = [[UILabel alloc]init];
    behind_times_label.text = @"次服用";
    behind_times_label.font = kFontLight(16);
    behind_times_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:behind_times_label];
    
    // 布局
    [before_times_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(container).offset(kHorizontalMargin);
        make.width.mas_equalTo(60);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(container);
    }];
    
    [_timesTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(before_times_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_times_label);
    }];
    
    [behind_times_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.timesTextField.mas_right);
        make.height.mas_equalTo(20);
        make.width.mas_equalTo(60);
        make.centerY.equalTo(before_times_label);
    }];
    
    // 添加事件监听
    [_timesTextField addTarget:self action:@selector(timesTextFieldTapped:) forControlEvents:UIControlEventTouchDown];
    [_timesTextField addTarget:self action:@selector(timesTextFieldTapped:) forControlEvents:UIControlEventTouchUpInside];
    
    __weak __typeof(self)weakSelf = self;
    [[_timesTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *text = [_timesTextField text];
        NSString *type = [NSString stringWithFormat:@"%@",kUsageKandYType_times];
        if(weakSelf.quickOrderTempId.length > 0){
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
}

// 设置用药时间行
- (void)setupTimeRowInContainer:(UIView *)container {
    UILabel *before_time_label = [[UILabel alloc]init];
    before_time_label.text = @"用药时间";
    before_time_label.font = kFontLight(16);
    before_time_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:before_time_label];
    
    _changeTimeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_changeTimeButton setTitle:@"饭后一小时" forState:UIControlStateNormal];
    [_changeTimeButton setTitleColor:[UIColor br_textBlackColor] forState:UIControlStateNormal];
    _changeTimeButton.titleLabel.font = kFontLight(17);
    [_changeTimeButton addTarget:self action:@selector(pressToChangeTime) forControlEvents:UIControlEventTouchUpInside];
    _changeTimeButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
    [container addSubview:_changeTimeButton];
    
    _changeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_changeButton setImage:[UIImage imageNamed:@"prescription_change_time_big"] forState:UIControlStateNormal];
    [_changeButton addTarget:self action:@selector(pressToChangeTime) forControlEvents:UIControlEventTouchUpInside];
    [container addSubview:_changeButton];
    
    // 布局
    [before_time_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(container).offset(kHorizontalMargin);
        make.centerY.equalTo(container);
        make.height.mas_equalTo(20);
        make.width.mas_equalTo(80);
    }];
    
    [_changeTimeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.changeButton.mas_left).offset(-10);
        make.centerY.equalTo(before_time_label);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(120);
    }];
    
    [_changeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(container).offset(-kHorizontalMargin);
        make.centerY.equalTo(before_time_label);
        make.height.and.width.mas_equalTo(30);
    }];
    
    self.before_time_label = before_time_label;
}

#pragma mark - 其他剂型 行设置方法

// 设置规格行
- (void)setupSpecificationRowInContainer:(UIView *)container {
    _specificationLabel = [[UILabel alloc] init];
    _specificationLabel.text = @"规     格:"; // 与“预计总重：”宽度保持一致
    _specificationLabel.textColor = [UIColor br_textBlackColor];
    _specificationLabel.font = kFontLight(15);
    [container addSubview:_specificationLabel];
    
    // 创建规格选择框容器
    _specificationSelectorView = [[UIView alloc] init];
    _specificationSelectorView.backgroundColor = [UIColor whiteColor];
    _specificationSelectorView.layer.cornerRadius = 6.0f;
    _specificationSelectorView.layer.borderWidth = 0.5f;
    _specificationSelectorView.layer.borderColor = [UIColor br_insideDivisionLineColor].CGColor;
    [container addSubview:_specificationSelectorView];
    
    // 创建占位文字标签
    _specificationPlaceholderLabel = [[UILabel alloc] init];
    _specificationPlaceholderLabel.text = @"请选择规格";
    _specificationPlaceholderLabel.textColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1.0];
    _specificationPlaceholderLabel.font = kFontLight(14);
    [_specificationSelectorView addSubview:_specificationPlaceholderLabel];
    
    // 创建下拉箭头
    _specificationArrowImageView = [[UIImageView alloc] init];
    _specificationArrowImageView.image = [UIImage imageNamed:@"down_jiantou"]; // 使用现有的向下箭头图片
    _specificationArrowImageView.contentMode = UIViewContentModeScaleAspectFit;
    [_specificationSelectorView addSubview:_specificationArrowImageView];
    
    // 设置规格行容器默认隐藏
    container.hidden = NO;
    
    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(specificationRowTapped:)];
    [_specificationSelectorView addGestureRecognizer:tapGesture];
    _specificationSelectorView.userInteractionEnabled = YES;
    
    // 布局规格标签
    [_specificationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(container).offset(kHorizontalMargin);
        make.width.mas_equalTo(80);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(container);
    }];
    
    // 布局规格选择框容器
    [_specificationSelectorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_specificationLabel.mas_right).offset(10);
        make.right.equalTo(container).offset(-kHorizontalMargin);
        make.height.mas_equalTo(32);
        make.centerY.equalTo(container);
    }];
    
    // 布局占位文字
    [_specificationPlaceholderLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_specificationSelectorView).offset(12);
        make.right.equalTo(_specificationArrowImageView.mas_left).offset(-8);
        make.centerY.equalTo(_specificationSelectorView);
    }];
    
    // 布局下拉箭头
    [_specificationArrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(_specificationSelectorView).offset(-12);
        make.width.height.mas_equalTo(12);
        make.centerY.equalTo(_specificationSelectorView);
    }];
}

// 设置辅料行
- (void)setupAuxiliaryMaterialRowInContainer:(UIView *)container {
    _auxiliaryMaterialLabel = [[UILabel alloc] init];
    _auxiliaryMaterialLabel.text = @"辅     料:"; // 与“预计总重：”宽度保持一致，
    _auxiliaryMaterialLabel.textColor = [UIColor br_textBlackColor];
    _auxiliaryMaterialLabel.font = kFontLight(15);
    _auxiliaryMaterialLabel.textAlignment = NSTextAlignmentJustified;
    _auxiliaryMaterialLabel.lineBreakMode = NSLineBreakByClipping;
    [container addSubview:_auxiliaryMaterialLabel];
    
    // 创建辅料选择框容器
    _auxiliaryMaterialSelectorView = [[UIView alloc] init];
    _auxiliaryMaterialSelectorView.backgroundColor = [UIColor whiteColor];
    _auxiliaryMaterialSelectorView.layer.cornerRadius = 6.0f;
    _auxiliaryMaterialSelectorView.layer.borderWidth = 0.0f;
    [container addSubview:_auxiliaryMaterialSelectorView];
    
    // 创建占位文字标签
    _auxiliaryMaterialPlaceholderLabel = [[UILabel alloc] init];
    _auxiliaryMaterialPlaceholderLabel.text = @"请选择辅料";
    _auxiliaryMaterialPlaceholderLabel.textColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1.0];
    _auxiliaryMaterialPlaceholderLabel.font = kFontLight(14);
    [_auxiliaryMaterialSelectorView addSubview:_auxiliaryMaterialPlaceholderLabel];
    
    // 创建向右箭头
    _auxiliaryMaterialArrowImageView = [[UIImageView alloc] init];
    _auxiliaryMaterialArrowImageView.image = [UIImage imageNamed:@"my_right"]; // 使用向右箭头图片
    _auxiliaryMaterialArrowImageView.contentMode = UIViewContentModeScaleAspectFit;
    [_auxiliaryMaterialSelectorView addSubview:_auxiliaryMaterialArrowImageView];
    
    // 设置辅料行容器默认隐藏
    container.hidden = NO;
    
    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(auxiliaryMaterialRowTapped:)];
    [_auxiliaryMaterialSelectorView addGestureRecognizer:tapGesture];
    _auxiliaryMaterialSelectorView.userInteractionEnabled = YES;
    
    // 布局辅料标签
    [_auxiliaryMaterialLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(container).offset(kHorizontalMargin);
        make.width.mas_equalTo(80);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(container);
    }];
    
    // 布局辅料选择框容器
    [_auxiliaryMaterialSelectorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_auxiliaryMaterialLabel.mas_right).offset(10);
        make.right.equalTo(container).offset(-kHorizontalMargin);
        make.height.mas_equalTo(32);
        make.centerY.equalTo(container);
    }];
    
    // 布局占位文字
    [_auxiliaryMaterialPlaceholderLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_auxiliaryMaterialSelectorView).offset(12);
        make.right.equalTo(_auxiliaryMaterialArrowImageView.mas_left).offset(-8);
        make.centerY.equalTo(_auxiliaryMaterialSelectorView);
    }];
    
    // 布局下拉箭头
    [_auxiliaryMaterialArrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(_auxiliaryMaterialSelectorView).offset(-12);
        make.width.height.mas_equalTo(12);
        make.centerY.equalTo(_auxiliaryMaterialSelectorView);
    }];
}

#pragma mark - 规格行控制方法

// 设置规格行的显示和隐藏
- (void)setSpecificationRowHidden:(BOOL)hidden {
    if (self.specificationLabel) {
        UIView *specificationContainer = self.specificationLabel.superview;
        
        // 更新容器高度约束
        CGFloat newHeight = hidden ? 0 : ROW_HEIGHT;
        [specificationContainer mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(newHeight);
        }];
        
        // 标记容器为隐藏状态（用于高度计算和虚线绘制判断）
        specificationContainer.hidden = hidden;
        
        // 重新计算并更新高度
        [self updateTotalHeight];
        
        // 重新绘制虚线（隐藏的行不绘制虚线）
        dispatch_async(dispatch_get_main_queue(), ^{
            [self drawAllDashLines];
        });
    }
}

#pragma mark - 辅料行控制方法

// 设置辅料行的显示和隐藏
- (void)setAuxiliaryMaterialRowHidden:(BOOL)hidden {
    if (self.auxiliaryMaterialLabel) {
        UIView *auxiliaryMaterialContainer = self.auxiliaryMaterialLabel.superview;
        
        // 更新容器高度约束
        CGFloat newHeight = hidden ? 0 : ROW_HEIGHT;
        [auxiliaryMaterialContainer mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(newHeight);
        }];
        
        // 标记容器为隐藏状态（用于高度计算和虚线绘制判断）
        auxiliaryMaterialContainer.hidden = hidden;
        
        // 重新计算并更新高度
        [self updateTotalHeight];
        
        // 重新绘制虚线（隐藏的行不绘制虚线）
        dispatch_async(dispatch_get_main_queue(), ^{
            [self drawAllDashLines];
        });
    }
}

// 设置其他剂型用法用量行（每日X次 每次Xg 预计服用X天 - 水平排列在同一行）
- (void)setupOtherUsageRowInContainer:(UIView *)container drugForm:(NSString *)drugForm {
    __weak __typeof(self)weakSelf = self;
    
    // 第一部分：每日XX次
    UILabel *before_times_label = [[UILabel alloc]init];
    before_times_label.text = @"每日";
    before_times_label.font = kFontLight(16);
    before_times_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:before_times_label];
    
    MMNumberKeyboard *timeskeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    timeskeyboard.allowsDecimalPoint = NO;
    timeskeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    timeskeyboard.delegate = self;
    _timesTextField = [[BRUnderlineRedTextField alloc]init];
    
    // 根据剂型设置默认值，这些特殊剂型保持3次，其他剂型为2次
    if ([drugForm isEqualToString:@"胶囊"] || 
        [drugForm isEqualToString:@"散剂"] || 
        [drugForm isEqualToString:@"水丸"] || 
        [drugForm isEqualToString:@"蜜丸"] || 
        [drugForm isEqualToString:@"膏方"]) {
        _timesTextField.text = @"3";
    } else {
        _timesTextField.text = @"2";
    }
    
    _timesTextField.delegate = self;
    _timesTextField.inputView = timeskeyboard;
    [container addSubview:_timesTextField];
    
    // 为 timesTextField 添加点击事件处理，在代煎剂型下跳出选择器
    [_timesTextField addTarget:self action:@selector(timesTextFieldTapped:) forControlEvents:UIControlEventTouchDown];
    [_timesTextField addTarget:self action:@selector(timesTextFieldTapped:) forControlEvents:UIControlEventTouchUpInside];
    
    UILabel *behind_times_label = [[UILabel alloc]init];
    behind_times_label.text = @"次";
    behind_times_label.font = kFontLight(16);
    behind_times_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:behind_times_label];
    
    // 第二部分：每次XXg
    UILabel *before_preDose_label = [[UILabel alloc]init];
    before_preDose_label.text = @"每次";
    before_preDose_label.font = kFontLight(16);
    before_preDose_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:before_preDose_label];
    
    MMNumberKeyboard *preDosekeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    preDosekeyboard.allowsDecimalPoint = NO;
    preDosekeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    preDosekeyboard.delegate = self;
    _preDoseTextField = [[BRUnderlineRedTextField alloc]init];
    _preDoseTextField.text = @"30";
    _preDoseTextField.delegate = self;
    _preDoseTextField.inputView = preDosekeyboard;
    [container addSubview:_preDoseTextField];
    
    _preDoseUnitLabel = [[UILabel alloc]init];
    _preDoseUnitLabel.text = @"g";
    _preDoseUnitLabel.font = kFontLight(16);
    _preDoseUnitLabel.textColor = [UIColor br_textBlackColor];
    [container addSubview:_preDoseUnitLabel];
    
    // 胶囊颗粒数标签已移除
    
    // 克数显示标签（显示计算后的克数，如 (6g)）
    _preDoseGramLabel = [[UILabel alloc] init];
    _preDoseGramLabel.textColor = [UIColor br_textRedColor];
    _preDoseGramLabel.font = kFontLight(15);
    _preDoseGramLabel.text = @"";
    _preDoseGramLabel.hidden = YES;
    [container addSubview:_preDoseGramLabel];
    
    // 第三部分：预计服用XX天
    UILabel *before_day_label = [[UILabel alloc]init];
    before_day_label.text = @"预计服用";
    before_day_label.font = kFontLight(16);
    before_day_label.textAlignment = NSTextAlignmentRight;
    before_day_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:before_day_label];
    
    _dayTextField = [[BRUnderlineRedTextField alloc]init];
    _dayTextField.font = kFontLight(16);
    _dayTextField.delegate = self;
    _dayTextField.text = @"20";
    _dayTextField.enabled = NO;
    [container addSubview:_dayTextField];
    
    UILabel *behind_day_label = [[UILabel alloc]init];
    behind_day_label.text = @"天";
    behind_day_label.font = kFontLight(16);
    behind_day_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:behind_day_label];
    
    UIImage *questionImage = [UIImage imageNamed:@"yangshengpu_jiangli"];
    UIButton *questionButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [questionButton setImage:questionImage forState:UIControlStateNormal];
    [container addSubview:questionButton];
    
    // 约束布局 - 水平排列
    [before_times_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(container).offset(kHorizontalMargin);
        make.width.mas_equalTo(40);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(container);
    }];
    
    [_timesTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(before_times_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_times_label);
    }];
    
    [behind_times_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_timesTextField.mas_right);
        make.height.and.width.mas_equalTo(20);
        make.centerY.equalTo(before_times_label);
    }];
    
    [before_preDose_label mas_makeConstraints:^(MASConstraintMaker *make) {
        if (isiPhone5) {
            make.left.equalTo(behind_times_label.mas_right).offset(5);
        }
        else {
            make.left.equalTo(behind_times_label.mas_right).offset(10);
        }
        make.width.mas_equalTo(40);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(container);
    }];
    
    [_preDoseTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(before_preDose_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_preDose_label);
    }];
    
    [_preDoseUnitLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_preDoseTextField.mas_right);
        if (isiPhone5) {
            make.height.and.width.mas_equalTo(15);
        }
        else {
            make.height.and.width.mas_equalTo(20);
        }
        make.centerY.equalTo(before_preDose_label);
    }];
    
    // 胶囊颗粒数标签布局约束已移除
    
    [_preDoseGramLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(_preDoseUnitLabel);
        make.left.equalTo(_preDoseUnitLabel.mas_right).with.offset(-5);
    }];
    
    [before_day_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(_dayTextField.mas_left);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(before_preDose_label);
    }];
    
    [_dayTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(behind_day_label.mas_left);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_day_label);
    }];
    
    [questionButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(questionImage.size);
        make.centerY.equalTo(before_day_label);
        make.right.equalTo(before_day_label.mas_left);
    }];
    
    [questionButton addTarget:self action:@selector(clickOtehrTypeMarkButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
    
    [behind_day_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(container).offset(-kHorizontalMargin);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(before_preDose_label);
    }];
    
    // 添加数据保存逻辑
    [[_timesTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *drugFormsMd5 = [[weakSelf.drugForm md5String] substringToIndex:5];
        NSString *type = [NSString stringWithFormat:@"%@_%@",kUsageOtherType_EveryDayTimes, drugFormsMd5];
        //如果为快速开方
        if(weakSelf.quickOrderTempId.length > 0){
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        NSString *text = [_timesTextField text];
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
    
    [[_preDoseTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *drugFormsMd5 = [[weakSelf.drugForm md5String] substringToIndex:5];
        NSString *type = [NSString stringWithFormat:@"%@_%@",kUsageOtherType_EveryTimesAmount, drugFormsMd5];
        //如果为快速开方
        if(weakSelf.quickOrderTempId.length > 0) {
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        NSString *text = [_preDoseTextField text];
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
        
        // 如果是瓶装膏方，同步输入值到直接重量字段
        if ([weakSelf.drugForm isEqualToString:@"膏方"] && weakSelf.isCreamFormulaBottlePackage) {
            double inputValue = [text doubleValue];
            weakSelf.creamFormulaDirectWeightValue = inputValue;
            [weakSelf updateCreamFormulaGramDisplay]; // 更新克数显示
        }
    }];
    
    [[_dayTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *drugFormsMd5 = [[weakSelf.drugForm md5String] substringToIndex:5];
        NSString *type = [NSString stringWithFormat:@"%@_%@",kUsageOtherType_EstimateDays, drugFormsMd5];
        //如果为快速开方
        if(weakSelf.quickOrderTempId.length > 0) {
            type = [NSString stringWithFormat:@"%@_%@",type,weakSelf.quickOrderTempId];
        }
        NSString *text = [_dayTextField text];
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
}

// 设置预计总重行
- (void)setupTotalDoseRowInContainer:(UIView *)container {
    UILabel *beforeTotalDoseLabel = [[UILabel alloc]init];
    beforeTotalDoseLabel.text = @"预计总重:";
    beforeTotalDoseLabel.textColor = [UIColor br_textBlackColor];
    beforeTotalDoseLabel.font = kFontLight(15);
    [container addSubview:beforeTotalDoseLabel];
    
    _totalDoseLabel = [[UILabel alloc]init];
    _totalDoseLabel.font = kFontLight(14);
    _totalDoseLabel.textColor = [UIColor br_textBlackColor];
    _totalDoseLabel.numberOfLines = 0;
    _totalDoseLabel.textAlignment = NSTextAlignmentRight;
    [container addSubview:_totalDoseLabel];
    
    // 布局
    [beforeTotalDoseLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(container).offset(kHorizontalMargin);
        make.width.mas_equalTo(80);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(container);
    }];
    
    [_totalDoseLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(beforeTotalDoseLabel.mas_right);
        make.right.equalTo(container).offset(-kHorizontalMargin);
        make.centerY.equalTo(beforeTotalDoseLabel);
    }];
}

// 设置每日XX次行
- (void)setupOtherTimesRowInContainer:(UIView *)container drugForm:(NSString *)drugForm {
    UILabel *before_times_label = [[UILabel alloc]init];
    before_times_label.text = @"每日";
    before_times_label.font = kFontLight(16);
    before_times_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:before_times_label];
    
    MMNumberKeyboard *timeskeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    timeskeyboard.allowsDecimalPoint = NO;
    timeskeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    timeskeyboard.delegate = self;
    _timesTextField = [[BRUnderlineRedTextField alloc]init];
    
    // 根据剂型设置默认值，这些特殊剂型保持3次，其他剂型为2次
    if ([drugForm isEqualToString:@"胶囊"] || 
        [drugForm isEqualToString:@"散剂"] || 
        [drugForm isEqualToString:@"水丸"] || 
        [drugForm isEqualToString:@"蜜丸"] || 
        [drugForm isEqualToString:@"膏方"]) {
        _timesTextField.text = @"3";
    } else {
        _timesTextField.text = @"2";
    }
    
    _timesTextField.delegate = self;
    _timesTextField.inputView = timeskeyboard;
    [container addSubview:_timesTextField];
    
    UILabel *behind_times_label = [[UILabel alloc]init];
    behind_times_label.text = @"次";
    behind_times_label.font = kFontLight(16);
    behind_times_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:behind_times_label];
    
    // 布局
    [before_times_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(container).offset(kHorizontalMargin);
        make.width.mas_equalTo(40);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(container);
    }];
    
    [_timesTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(before_times_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_times_label);
    }];
    
    [behind_times_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.timesTextField.mas_right);
        make.height.and.width.mas_equalTo(20);
        make.centerY.equalTo(before_times_label);
    }];
    
    // 添加事件监听
    [_timesTextField addTarget:self action:@selector(timesTextFieldTapped:) forControlEvents:UIControlEventTouchDown];
    [_timesTextField addTarget:self action:@selector(timesTextFieldTapped:) forControlEvents:UIControlEventTouchUpInside];
    
    __weak __typeof(self)weakSelf = self;
    [[_timesTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *drugFormsMd5 = [[weakSelf.drugForm md5String] substringToIndex:5];
        NSString *type = [NSString stringWithFormat:@"%@_%@",kUsageOtherType_EveryDayTimes, drugFormsMd5];
        
        if(weakSelf.quickOrderTempId.length > 0){
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        
        NSString *text = [_timesTextField text];
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
}

// 设置每次XXg行
- (void)setupOtherDoseRowInContainer:(UIView *)container drugForm:(NSString *)drugForm {
    UILabel *before_preDose_label = [[UILabel alloc]init];
    before_preDose_label.text = @"每次";
    before_preDose_label.font = kFontLight(16);
    before_preDose_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:before_preDose_label];
    
    MMNumberKeyboard *preDosekeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    preDosekeyboard.allowsDecimalPoint = NO;
    preDosekeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    preDosekeyboard.delegate = self;
    _preDoseTextField = [[BRUnderlineRedTextField alloc]init];
    _preDoseTextField.text = @"30";
    _preDoseTextField.delegate = self;
    _preDoseTextField.inputView = preDosekeyboard;
    [container addSubview:_preDoseTextField];
    
    _preDoseUnitLabel = [[UILabel alloc]init];
    _preDoseUnitLabel.text = @"g";
    _preDoseUnitLabel.font = kFontLight(16);
    _preDoseUnitLabel.textColor = [UIColor br_textBlackColor];
    [container addSubview:_preDoseUnitLabel];
    
    // 胶囊颗粒数标签已移除
    
    // 布局
    [before_preDose_label mas_makeConstraints:^(MASConstraintMaker *make) {
        if (isiPhone5) {
            make.left.equalTo(container).offset(kHorizontalMargin + 100);
        } else {
            make.left.equalTo(container).offset(kHorizontalMargin + 110);
        }
        make.width.mas_equalTo(40);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(container);
    }];
    
    [_preDoseTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(before_preDose_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_preDose_label);
    }];
    
    [_preDoseUnitLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.preDoseTextField.mas_right);
        if (isiPhone5) {
            make.height.and.width.mas_equalTo(15);
        } else {
            make.height.and.width.mas_equalTo(20);
        }
        make.centerY.equalTo(before_preDose_label);
    }];
    
    // 胶囊颗粒数标签布局约束已移除
    
    [_preDoseGramLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(_preDoseUnitLabel);
        make.left.equalTo(_preDoseUnitLabel.mas_right).with.offset(-5);
    }];
    
    // 添加事件监听
    __weak __typeof(self)weakSelf = self;
    [[_preDoseTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *drugFormsMd5 = [[weakSelf.drugForm md5String] substringToIndex:5];
        NSString *type = [NSString stringWithFormat:@"%@_%@",kUsageOtherType_EveryTimesAmount, drugFormsMd5];
        
        if(weakSelf.quickOrderTempId.length > 0) {
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        
        NSString *text = [_preDoseTextField text];
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
        
        // 如果是瓶装膏方，同步输入值到直接重量字段
        if ([weakSelf.drugForm isEqualToString:@"膏方"] && weakSelf.isCreamFormulaBottlePackage) {
            double inputValue = [text doubleValue];
            weakSelf.creamFormulaDirectWeightValue = inputValue;
            [weakSelf updateCreamFormulaGramDisplay]; // 更新克数显示
        }
    }];
    
    // 根据剂型设置特殊事件监听
    if ([drugForm isEqualToString:@"蜜丸"] || [drugForm isEqualToString:@"水丸"]) {
        _preDoseTextField.text = @"6";
        [_preDoseTextField addTarget:self action:@selector(textFieldEditingDidBegin:) forControlEvents:UIControlEventEditingDidBegin];
    }
    else if ([drugForm isEqualToString:@"胶囊"]){
        _preDoseTextField.text = @"3";
        [_preDoseTextField addTarget:self action:@selector(textFieldEditingDidBegin:) forControlEvents:UIControlEventEditingDidBegin];
    }
    else if ([drugForm isEqualToString:@"散剂"]) {
        _preDoseTextField.text = @"10";
    }
    else if ([drugForm isEqualToString:@"膏方"]) {
        _preDoseTextField.text = @"30";
    }
}

// 设置预计服用XX天行
- (void)setupOtherDaysRowInContainer:(UIView *)container {
    UILabel *before_day_label = [[UILabel alloc]init];
    before_day_label.text = @"预计服用";
    before_day_label.font = kFontLight(16);
    before_day_label.textAlignment = NSTextAlignmentRight;
    before_day_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:before_day_label];
    
    _dayTextField = [[BRUnderlineRedTextField alloc]init];
    _dayTextField.font = kFontLight(16);
    _dayTextField.delegate = self;
    _dayTextField.text = @"20";
    _dayTextField.enabled = NO;
    [container addSubview:_dayTextField];
    
    UILabel *behind_day_label = [[UILabel alloc]init];
    behind_day_label.text = @"天";
    behind_day_label.font = kFontLight(16);
    behind_day_label.textColor = [UIColor br_textBlackColor];
    [container addSubview:behind_day_label];
    
    UIImage *questionImage = [UIImage imageNamed:@"yangshengpu_jiangli"];
    UIButton *questionButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [questionButton setImage:questionImage forState:UIControlStateNormal];
    [container addSubview:questionButton];
    
    // 布局
    [behind_day_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(container).offset(-kHorizontalMargin);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(container);
    }];
    
    [_dayTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(behind_day_label.mas_left);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(container);
    }];
    
    [before_day_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(_dayTextField.mas_left);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(container);
    }];
    
    [questionButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(questionImage.size);
        make.centerY.equalTo(container);
        make.right.equalTo(before_day_label.mas_left);
    }];
    
    [questionButton addTarget:self action:@selector(clickOtehrTypeMarkButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
    
    // 添加事件监听
    __weak __typeof(self)weakSelf = self;
    [[_dayTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *drugFormsMd5 = [[weakSelf.drugForm md5String] substringToIndex:5];
        NSString *type = [NSString stringWithFormat:@"%@_%@",kUsageOtherType_EstimateDays, drugFormsMd5];
        
        if(weakSelf.quickOrderTempId.length > 0) {
            type = [NSString stringWithFormat:@"%@_%@",type,weakSelf.quickOrderTempId];
        }
        
        NSString *text = [_dayTextField text];
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
}

- (void)textFieldEditChanged:(UITextField *)textField {
    NSString *toBeString = textField.text;
    if (_drugChanged) {
        _drugChanged(toBeString);
    }
}

- (void)textFieldEditingDidBegin:(UITextField *)textField {
    if (_textFieldEditingDidBegin) {
        _textFieldEditingDidBegin();
    }
}

// 胶囊输入框编辑开始方法已移除

- (BOOL)numberKeyboard:(MMNumberKeyboard *)numberKeyboard shouldInsertText:(NSString *)text {
    
    if ([_dayTextField isFirstResponder]) {
        return NO;
    }
    
    return YES;
    
}

- (void)textFieldDidEndEditing:(UITextField *)textField {
    
    if (textField == _drugNumTextField) {
        
        NSString *drugNum = [_drugNumTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        
        if (drugNum.length == 0 || [drugNum integerValue] == 0) {
            
        }
        else {
        
            NSString *usageNum = [_usageTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
            
            _drugChanged(drugNum);
            
            if (usageNum.length == 0 || [usageNum integerValue] == 0) {
                if (_changeDay) {
                    _changeDay(drugNum);
                }
            }
            else {
                int times = [drugNum intValue]/[usageNum intValue];
                if (_changeDay) {
                    _changeDay([NSString stringWithFormat:@"%d",times == 0? 1:times]);
                }
            }
            
        }
        
    }
    
    if (textField  == _usageTextField) {
        
        NSString *usageNum = [_usageTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        if (usageNum.length == 0 || [usageNum integerValue] == 0) {
            
        }
        else {
            
            NSString *drugNum = [_drugNumTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
            if (drugNum.length == 0 || [usageNum integerValue] ==0) {
                
            }
            else {
                int times = [drugNum intValue]/[usageNum intValue];
                if (_changeDay) {
                    _changeDay([NSString stringWithFormat:@"%d",times == 0? 1:times]);
                }
            }
            
        }
        
    }
    
    if ((textField == _timesTextField || textField == _preDoseTextField) &&
        ![_drugForm isEqualToString:@"颗粒"] &&
        ![_drugForm isEqualToString:@"饮片"] &&
        ![_drugForm isEqualToString:@"代煎"] &&
        ![_drugForm isEqualToString:@"外用中药"]) {
        
        if (_totalDoseLabel.text.length == 0) {
            
        }
        else {
            
            NSString *doseStr = [[_totalDoseLabel.text componentsSeparatedByString:@"g"] firstObject];
            
            if ([doseStr floatValue] == 0) {
                
            }
            else {
                
                NSString *timesNum = [_timesTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
                NSString *preDoseNum = [_preDoseTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
                if ([timesNum intValue] == 0 || [preDoseNum integerValue] == 0 || timesNum.length == 0 || preDoseNum.length == 0) {
                    
                }
                else {
                    
                    CGFloat totalDose = [doseStr integerValue];
                    NSInteger day = totalDose/([timesNum integerValue]*[preDoseNum floatValue]);
                    if (_changeDay) {
                        _changeDay([NSString stringWithFormat:@"%ld",day == 0? 1:day]);
                    }
                    
                }
                
            }
            
        }
        
    }
    
}

#pragma mark - click event
- (void)clickOtehrTypeMarkButtonEvent:(UIButton *)sender {
    if (self.clickOtherTypeBlock) {
        self.clickOtherTypeBlock();
    }
}

// 添加timesTextField的点击处理方法
- (void)timesTextFieldTapped:(UITextField *)sender {
    // 判断当前剂型是否为代煎
    if ([_drugForm isEqualToString:@"代煎"]) {
        // 如果是代煎剂型，执行回调打开次数选择器
        if (self.servingTimesSelectionBlock) {
            NSLog(@"触发代煎次数选择器");
            self.servingTimesSelectionBlock();
            // 阻止默认的键盘弹出
            [sender resignFirstResponder];
        }
    }
}

// 在textFieldShouldBeginEditing方法中添加对代煎剂型"每剂分几次服用"输入框的特殊处理
- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField {
    // 如果是timesTextField（每剂分几次服用）且剂型为代煎，则不允许键盘弹出
    if (textField == _timesTextField && [_drugForm isEqualToString:@"代煎"]) {
        NSLog(@"阻止代煎剂型下的timesTextField显示键盘");
        // 触发选择器
        if (self.servingTimesSelectionBlock) {
            self.servingTimesSelectionBlock();
        }
        return NO;
    }
    
    // 如果是preDoseTextField且有规格数据，则弹出数量选择弹窗
    if (textField == _preDoseTextField) {
        if ([_drugForm isEqualToString:@"蜜丸"] && self.currentSpecGramPerUnit > 0) {
            NSLog(@"弹出蜜丸数量选择弹窗");
            [self showPillUnitCountSelection];
            return NO;
        } else if ([_drugForm isEqualToString:@"胶囊"] && self.currentCapsuleSpecGramPerUnit > 0) {
            NSLog(@"弹出胶囊数量选择弹窗");
            [self showCapsuleUnitCountSelection];
            return NO;
        } else if ([_drugForm isEqualToString:@"水丸"] && self.currentWaterPillSpecGramPerUnit > 0) {
            NSLog(@"弹出水丸数量选择弹窗");
            [self showWaterPillUnitCountSelection];
            return NO;
        } else if ([_drugForm isEqualToString:@"膏方"] && self.currentCreamFormulaSpecGramPerUnit > 0) {
            if (self.isCreamFormulaBottlePackage) {
                // 瓶装膏方：直接弹出键盘，允许手动输入
                NSLog(@"瓶装膏方：直接弹出键盘输入");
                return YES;
            } else {
                // 非瓶装膏方：弹出选择弹窗
                NSLog(@"弹出膏方数量选择弹窗");
                [self showCreamFormulaUnitCountSelection];
                return NO;
            }
        }
    }
    
    return YES;
}

#pragma mark - 规格行点击事件处理

/**
 * 规格行点击事件处理
 */
- (void)specificationRowTapped:(UITapGestureRecognizer *)gesture {
    if (self.specificationSelectionBlock) {
        self.specificationSelectionBlock();
    }
}

/**
 * 更新规格显示文本
 */
- (void)updateSpecificationDisplayText:(NSString *)specText {
    if (self.specificationPlaceholderLabel) {
        self.specificationPlaceholderLabel.text = specText;
        
        // 根据是否选择了规格来设置颜色
        if (specText && ![specText isEqualToString:@"请选择规格"]) {
            self.specificationPlaceholderLabel.textColor = [UIColor br_mainBlueColor]; // 蓝色
        } else {
            self.specificationPlaceholderLabel.textColor = [UIColor br_textLightGrayColor]; // 灰色
        }
    }
}

#pragma mark - 规格业务逻辑方法

/**
 * 更新厂商模型并初始化规格数据
 * @param factoryModel 厂商模型
 */
- (void)updateSpecificationWithFactoryModel:(BRSubFactoryModel *)factoryModel {
    self.currentFactoryModel = factoryModel;
    self.packageSpecList = factoryModel.packageSpecList;
    
    // 重置选中状态
    self.selectedSpecification = @"";
    
    // 初始化规格数据
    [self initSpecificationData];
    
    // 根据剂型和规格数据判断是否显示规格行
    BOOL shouldShow = [self shouldShowSpecificationRowForDrugForm:self.drugForm];
    [self setSpecificationRowHidden:!shouldShow];
    
    NSLog(@"更新规格信息 - 剂型: %@, 规格数量: %lu, 显示规格行: %@", 
          self.drugForm, 
          (unsigned long)(self.packageSpecList ? self.packageSpecList.count : 0),
          shouldShow ? @"是" : @"否");
}

/**
 * 初始化规格选择数据
 */
- (void)initSpecificationData {
    NSLog(@"initSpecificationData - 剂型: %@, 规格数量: %lu", 
          self.drugForm ?: @"未设置", 
          (unsigned long)(self.packageSpecList ? self.packageSpecList.count : 0));
    
    if (!self.packageSpecList || self.packageSpecList.count == 0) {
        // 没有规格数据时
        self.selectedSpecification = @"";
        [self updateSpecificationDisplayText:@"请选择规格"];
        NSLog(@"无规格数据，设置为请选择规格");
        return;
    }
    
    BOOL hasCheckedSpec = NO;
    
    // 检查是否有默认选中的规格
    for (BRPackageSpecModel *packageSpec in self.packageSpecList) {
        if (packageSpec.value && packageSpec.value.length > 0) {
            NSLog(@"检查规格: %@, checked: %@", packageSpec.value, packageSpec.checked ? @"是" : @"否");
            if (packageSpec.checked && !hasCheckedSpec) {
                hasCheckedSpec = YES;
                self.selectedSpecification = packageSpec.value;
                [self updateSpecificationDisplayText:packageSpec.value];
                // 立即解析默认选中的规格
                [self parseSpecificationForCurrentDrugForm:packageSpec.value];
                NSLog(@"使用默认选中规格: %@", packageSpec.value);
                break;
            }
        }
    }
    
    // 如果没有选中的规格，使用第一个作为默认选中项
    if (!hasCheckedSpec && self.packageSpecList.count > 0) {
        BRPackageSpecModel *firstSpec = self.packageSpecList.firstObject;
        if (firstSpec.value && firstSpec.value.length > 0) {
            self.selectedSpecification = firstSpec.value;
            [self updateSpecificationDisplayText:firstSpec.value];
            // 立即解析默认规格
            [self parseSpecificationForCurrentDrugForm:firstSpec.value];
            NSLog(@"使用第一个规格作为默认: %@", firstSpec.value);
        }
    }
}

/**
 * 判断指定剂型是否应该显示规格行
 * @param drugForm 剂型名称
 * @return 是否应该显示规格行
 */
- (BOOL)shouldShowSpecificationRowForDrugForm:(NSString *)drugForm {
    if (!drugForm || drugForm.length == 0) {
        return NO;
    }
    
    // 检查是否有规格数据
    if (!self.packageSpecList || self.packageSpecList.count == 0) {
        return NO;
    }
    
    // 仅在特殊剂型时显示规格行：蜜丸、水丸、胶囊、膏方、散剂
    NSArray *specialDrugForms = @[@"蜜丸", @"水丸", @"胶囊", @"膏方", @"散剂"];
    
    for (NSString *specialForm in specialDrugForms) {
        if ([drugForm isEqualToString:specialForm]) {
            return YES;
        }
    }
    
    return NO;
}

#pragma mark - 规格解析方法

/**
 * 通用规格解析方法（根据当前剂型自动选择相应的解析方法）
 * @param specValue 规格值，如"3g/丸"、"0.5g/粒"等
 */
- (void)parseSpecificationForCurrentDrugForm:(NSString *)specValue {
    if (!specValue || specValue.length == 0 || !self.drugForm) {
        return;
    }
    
    NSLog(@"开始解析规格: %@ for 剂型: %@", specValue, self.drugForm);
    
    if ([self.drugForm isEqualToString:@"蜜丸"]) {
        [self parseSpecificationData:specValue];
    } else if ([self.drugForm isEqualToString:@"胶囊"]) {
        [self parseCapsuleSpecificationData:specValue];
    } else if ([self.drugForm isEqualToString:@"水丸"]) {
        [self parseWaterPillSpecificationData:specValue];
    } else if ([self.drugForm isEqualToString:@"膏方"]) {
        [self parseCreamFormulaSpecificationData:specValue];
    } else {
        NSLog(@"不支持的剂型: %@", self.drugForm);
    }
}

/**
 * 解析规格数据（蜜丸剂型）
 * 格式示例: "3g/丸", "500mg/粒", "15ml/瓶"
 */
- (void)parseSpecificationData:(NSString *)specValue {
    if (!specValue || specValue.length == 0) {
        return;
    }
    
    // 使用正则表达式解析规格 "数字+重量单位/包装单位" 格式
    NSString *pattern = @"([0-9.]+)([a-zA-Z]+)/(.+)";
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:pattern options:0 error:nil];
    NSArray *matches = [regex matchesInString:specValue options:0 range:NSMakeRange(0, specValue.length)];
    
    if (matches.count > 0) {
        NSTextCheckingResult *match = matches.firstObject;
        NSRange weightValueRange = [match rangeAtIndex:1]; // 重量数值
        NSRange weightUnitRange = [match rangeAtIndex:2];  // 重量单位
        NSRange packageUnitRange = [match rangeAtIndex:3]; // 包装单位
        
        if (weightValueRange.location != NSNotFound && 
            weightUnitRange.location != NSNotFound && 
            packageUnitRange.location != NSNotFound) {
            
            NSString *weightValueString = [specValue substringWithRange:weightValueRange];
            NSString *weightUnitString = [specValue substringWithRange:weightUnitRange];
            NSString *packageUnitString = [specValue substringWithRange:packageUnitRange];
            
            self.currentSpecGramPerUnit = [weightValueString doubleValue];
            self.currentSpecWeightUnit = weightUnitString;
            self.currentSpecUnit = packageUnitString;
            self.selectedUnitCount = 1; // 默认选择1个单位
            
            NSLog(@"蜜丸规格解析: %@ -> %.1f%@/%@", specValue, self.currentSpecGramPerUnit, self.currentSpecWeightUnit, self.currentSpecUnit);
            
            [self updatePillDosageDisplay];
            [self updatePillGramDisplay];
        }
    }
}

/**
 * 解析胶囊规格数据
 * 格式示例: "0.5g/粒", "250mg/粒"
 */
- (void)parseCapsuleSpecificationData:(NSString *)specValue {
    if (!specValue || specValue.length == 0) {
        return;
    }
    
    // 使用正则表达式解析规格 "数字+重量单位/包装单位" 格式
    NSString *pattern = @"([0-9.]+)([a-zA-Z]+)/(.+)";
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:pattern options:0 error:nil];
    NSArray *matches = [regex matchesInString:specValue options:0 range:NSMakeRange(0, specValue.length)];
    
    if (matches.count > 0) {
        NSTextCheckingResult *match = matches.firstObject;
        NSRange weightValueRange = [match rangeAtIndex:1]; // 重量数值
        NSRange weightUnitRange = [match rangeAtIndex:2];  // 重量单位
        NSRange packageUnitRange = [match rangeAtIndex:3]; // 包装单位
        
        if (weightValueRange.location != NSNotFound && 
            weightUnitRange.location != NSNotFound && 
            packageUnitRange.location != NSNotFound) {
            
            NSString *weightValueString = [specValue substringWithRange:weightValueRange];
            NSString *weightUnitString = [specValue substringWithRange:weightUnitRange];
            NSString *packageUnitString = [specValue substringWithRange:packageUnitRange];
            
            self.currentCapsuleSpecGramPerUnit = [weightValueString doubleValue];
            self.currentCapsuleSpecWeightUnit = weightUnitString;
            self.currentCapsuleSpecUnit = packageUnitString;
            self.selectedCapsuleUnitCount = 3; // 默认选择3粒
            
            NSLog(@"胶囊规格解析: %@ -> %.1f%@/%@", specValue, self.currentCapsuleSpecGramPerUnit, self.currentCapsuleSpecWeightUnit, self.currentCapsuleSpecUnit);
            
            [self updateCapsuleDosageDisplay];
            [self updateCapsuleGramDisplay];
        }
    }
}

/**
 * 解析水丸规格数据
 * 格式示例: "3g/丸", "500mg/丸"
 */
- (void)parseWaterPillSpecificationData:(NSString *)specValue {
    if (!specValue || specValue.length == 0) {
        return;
    }
    
    // 使用正则表达式解析规格 "数字+重量单位/包装单位" 格式
    NSString *pattern = @"([0-9.]+)([a-zA-Z]+)/(.+)";
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:pattern options:0 error:nil];
    NSArray *matches = [regex matchesInString:specValue options:0 range:NSMakeRange(0, specValue.length)];
    
    if (matches.count > 0) {
        NSTextCheckingResult *match = matches.firstObject;
        NSRange weightValueRange = [match rangeAtIndex:1]; // 重量数值
        NSRange weightUnitRange = [match rangeAtIndex:2];  // 重量单位
        NSRange packageUnitRange = [match rangeAtIndex:3]; // 包装单位
        
        if (weightValueRange.location != NSNotFound && 
            weightUnitRange.location != NSNotFound && 
            packageUnitRange.location != NSNotFound) {
            
            NSString *weightValueString = [specValue substringWithRange:weightValueRange];
            NSString *weightUnitString = [specValue substringWithRange:weightUnitRange];
            NSString *packageUnitString = [specValue substringWithRange:packageUnitRange];
            
            self.currentWaterPillSpecGramPerUnit = [weightValueString doubleValue];
            self.currentWaterPillSpecWeightUnit = weightUnitString;
            self.currentWaterPillSpecUnit = packageUnitString;
            self.selectedWaterPillUnitCount = 1; // 默认选择1丸
            
            NSLog(@"水丸规格解析: %@ -> %.1f%@/%@", specValue, self.currentWaterPillSpecGramPerUnit, self.currentWaterPillSpecWeightUnit, self.currentWaterPillSpecUnit);
            
            [self updateWaterPillDosageDisplay];
            [self updateWaterPillGramDisplay];
        }
    }
}

/**
 * 解析膏方规格数据
 * 格式示例: "10g/勺", "15ml/勺"
 */
- (void)parseCreamFormulaSpecificationData:(NSString *)specValue {
    if (!specValue || specValue.length == 0) {
        return;
    }
    
    // 使用正则表达式解析规格 "数字+重量单位/包装单位" 格式
    NSString *pattern = @"([0-9.]+)([a-zA-Z]+)/(.+)";
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:pattern options:0 error:nil];
    NSArray *matches = [regex matchesInString:specValue options:0 range:NSMakeRange(0, specValue.length)];
    
    if (matches.count > 0) {
        NSTextCheckingResult *match = matches.firstObject;
        NSRange weightValueRange = [match rangeAtIndex:1]; // 重量数值
        NSRange weightUnitRange = [match rangeAtIndex:2];  // 重量单位
        NSRange packageUnitRange = [match rangeAtIndex:3]; // 包装单位
        
        if (weightValueRange.location != NSNotFound && 
            weightUnitRange.location != NSNotFound && 
            packageUnitRange.location != NSNotFound) {
            
            NSString *weightValueString = [specValue substringWithRange:weightValueRange];
            NSString *weightUnitString = [specValue substringWithRange:weightUnitRange];
            NSString *packageUnitString = [specValue substringWithRange:packageUnitRange];
            
            self.currentCreamFormulaSpecGramPerUnit = [weightValueString doubleValue];
            self.currentCreamFormulaSpecWeightUnit = weightUnitString;
            self.currentCreamFormulaSpecUnit = packageUnitString;
            
            // 判断是否为瓶装膏方
            self.isCreamFormulaBottlePackage = [packageUnitString isEqualToString:@"瓶"];
            
            if (self.isCreamFormulaBottlePackage) {
                // 瓶装膏方：直接重量数值默认为15
                self.creamFormulaDirectWeightValue = 15.0;
                self.selectedCreamFormulaUnitCount = 1; // 保持1瓶，但实际显示和计算用直接重量
            } else {
                // 非瓶装膏方默认选择1勺
                self.selectedCreamFormulaUnitCount = 1;
                self.creamFormulaDirectWeightValue = 0; // 非瓶装不使用直接重量
            }
            
            NSLog(@"膏方规格解析: %@ -> %.1f%@/%@, 瓶装: %@", specValue, self.currentCreamFormulaSpecGramPerUnit, self.currentCreamFormulaSpecWeightUnit, self.currentCreamFormulaSpecUnit, self.isCreamFormulaBottlePackage ? @"是" : @"否");
            
            [self updateCreamFormulaDosageDisplay];
            [self updateCreamFormulaGramDisplay];
        }
    }
}

#pragma mark - 用量显示更新方法

/**
 * 更新蜜丸剂型的用量显示
 */
- (void)updatePillDosageDisplay {
    if (!self.currentSpecUnit || self.currentSpecGramPerUnit <= 0) {
        return;
    }
    
    // 更新"每次X丸"的显示
    NSString *dosageText = [NSString stringWithFormat:@"%ld", (long)self.selectedUnitCount];
    self.preDoseTextField.text = dosageText;
    
    // 更新单位标签显示包装单位
    self.preDoseUnitLabel.text = self.currentSpecUnit;
    
    NSLog(@"更新蜜丸用量显示: 每次%@%@", dosageText, self.currentSpecUnit);
}

/**
 * 更新胶囊剂型的用量显示
 */
- (void)updateCapsuleDosageDisplay {
    if (!self.currentCapsuleSpecUnit || self.currentCapsuleSpecGramPerUnit <= 0) {
        return;
    }
    
    // 更新"每次X粒"的数量显示
    NSString *dosageText = [NSString stringWithFormat:@"%ld", (long)self.selectedCapsuleUnitCount];
    self.preDoseTextField.text = dosageText;
    
    // 更新单位标签显示包装单位  
    self.preDoseUnitLabel.text = self.currentCapsuleSpecUnit;
    
    NSLog(@"更新胶囊用量显示: %@%@", dosageText, self.currentCapsuleSpecUnit);
}

/**
 * 更新水丸剂型的用量显示
 */
- (void)updateWaterPillDosageDisplay {
    if (!self.currentWaterPillSpecUnit || self.currentWaterPillSpecGramPerUnit <= 0) {
        return;
    }
    
    // 更新"每次X丸"的显示
    NSString *dosageText = [NSString stringWithFormat:@"%ld", (long)self.selectedWaterPillUnitCount];
    self.preDoseTextField.text = dosageText;
    
    // 更新单位标签显示包装单位
    self.preDoseUnitLabel.text = self.currentWaterPillSpecUnit;
    
    NSLog(@"更新水丸用量显示: 每次%@%@", dosageText, self.currentWaterPillSpecUnit);
}

/**
 * 更新膏方剂型的用量显示
 */
- (void)updateCreamFormulaDosageDisplay {
    if (!self.currentCreamFormulaSpecUnit || self.currentCreamFormulaSpecGramPerUnit <= 0) {
        return;
    }
    
    if (self.isCreamFormulaBottlePackage) {
        // 瓶装膏方：显示直接重量数值（如 "每次15g" 或 "每次15ml"）
        // 根据需求，瓶装膏方统一使用"g"作为单位
        NSString *dosageText;
        if (self.creamFormulaDirectWeightValue == (int)self.creamFormulaDirectWeightValue) {
            dosageText = [NSString stringWithFormat:@"%.0f", self.creamFormulaDirectWeightValue];
        } else {
            dosageText = [NSString stringWithFormat:@"%.1f", self.creamFormulaDirectWeightValue];
        }
        self.preDoseTextField.text = dosageText;
        
        // 瓶装膏方统一使用"g"作为单位显示
        self.preDoseUnitLabel.text = @"g";
        
        NSLog(@"更新瓶装膏方用量显示: 每次%@g", dosageText);
    } else {
        // 非瓶装膏方：保持原有显示（如 "每次X勺"）
        NSString *dosageText = [NSString stringWithFormat:@"%ld", (long)self.selectedCreamFormulaUnitCount];
        self.preDoseTextField.text = dosageText;
        
        // 更新单位标签显示包装单位
        self.preDoseUnitLabel.text = self.currentCreamFormulaSpecUnit;
        
        NSLog(@"更新膏方用量显示: 每次%@%@", dosageText, self.currentCreamFormulaSpecUnit);
    }
}

#pragma mark - 克数显示更新方法

/**
 * 更新蜜丸剂型的克数显示
 */
- (void)updatePillGramDisplay {
    // 蜜丸剂型隐藏克数显示
    self.preDoseGramLabel.hidden = YES;
    
    NSLog(@"蜜丸剂型隐藏克数显示");
}

/**
 * 更新胶囊剂型的克数显示
 */
- (void)updateCapsuleGramDisplay {
    // 胶囊剂型隐藏克数显示
    self.preDoseGramLabel.hidden = YES;
    
    NSLog(@"胶囊剂型隐藏克数显示");
}

/**
 * 更新水丸剂型的克数显示
 */
- (void)updateWaterPillGramDisplay {
    // 水丸剂型隐藏克数显示
    self.preDoseGramLabel.hidden = YES;
    
    NSLog(@"水丸剂型隐藏克数显示");
}
    
/**
 * 更新膏方剂型的克数显示
 */
- (void)updateCreamFormulaGramDisplay {
    // 膏方剂型隐藏克数显示
    self.preDoseGramLabel.hidden = YES;
    
    NSLog(@"膏方剂型隐藏克数显示");
}

#pragma mark - 数量选择弹窗方法

/**
 * 显示蜜丸数量选择弹窗（1、2、3丸）
 */
- (void)showPillUnitCountSelection {
    if (!self.currentSpecUnit || self.currentSpecGramPerUnit <= 0) {
        return;
    }
    
    NSArray *options = @[@"1", @"2", @"3"];
    NSMutableArray *displayOptions = [NSMutableArray array];
    
    for (NSString *count in options) {
        double totalGram = [count integerValue] * self.currentSpecGramPerUnit;
        NSString *displayText = [NSString stringWithFormat:@"%@%@(%.1f%@)", 
                                count, self.currentSpecUnit, totalGram, self.currentSpecWeightUnit];
        [displayOptions addObject:displayText];
    }
    
    [self showUnitSelectionWithOptions:displayOptions completion:^(NSInteger selectedIndex) {
        self.selectedUnitCount = [options[selectedIndex] integerValue];
        [self updatePillDosageDisplay];
        [self updatePillGramDisplay];
        NSLog(@"选择蜜丸数量: %ld%@", (long)self.selectedUnitCount, self.currentSpecUnit);
    }];
}

/**
 * 显示胶囊数量选择弹窗（3、6、9粒）
 */
- (void)showCapsuleUnitCountSelection {
    if (!self.currentCapsuleSpecUnit || self.currentCapsuleSpecGramPerUnit <= 0) {
        return;
    }
    
    NSArray *options = @[@"3", @"6", @"9"];
    NSMutableArray *displayOptions = [NSMutableArray array];
    
    for (NSString *count in options) {
        double totalGram = [count integerValue] * self.currentCapsuleSpecGramPerUnit;
        NSString *displayText = [NSString stringWithFormat:@"%@%@(%.1f%@)", 
                                count, self.currentCapsuleSpecUnit, totalGram, self.currentCapsuleSpecWeightUnit];
        [displayOptions addObject:displayText];
    }
    
    [self showUnitSelectionWithOptions:displayOptions completion:^(NSInteger selectedIndex) {
        self.selectedCapsuleUnitCount = [options[selectedIndex] integerValue];
        [self updateCapsuleDosageDisplay];
        [self updateCapsuleGramDisplay];
        NSLog(@"选择胶囊数量: %ld%@", (long)self.selectedCapsuleUnitCount, self.currentCapsuleSpecUnit);
    }];
}

/**
 * 显示水丸数量选择弹窗（1、2、3丸）
 */
- (void)showWaterPillUnitCountSelection {
    if (!self.currentWaterPillSpecUnit || self.currentWaterPillSpecGramPerUnit <= 0) {
        return;
    }
    
    NSArray *options = @[@"1", @"2", @"3"];
    NSMutableArray *displayOptions = [NSMutableArray array];
    
    for (NSString *count in options) {
        double totalGram = [count integerValue] * self.currentWaterPillSpecGramPerUnit;
        NSString *displayText = [NSString stringWithFormat:@"%@%@(%.1f%@)", 
                                count, self.currentWaterPillSpecUnit, totalGram, self.currentWaterPillSpecWeightUnit];
        [displayOptions addObject:displayText];
    }
    
    [self showUnitSelectionWithOptions:displayOptions completion:^(NSInteger selectedIndex) {
        self.selectedWaterPillUnitCount = [options[selectedIndex] integerValue];
        [self updateWaterPillDosageDisplay];
        [self updateWaterPillGramDisplay];
        NSLog(@"选择水丸数量: %ld%@", (long)self.selectedWaterPillUnitCount, self.currentWaterPillSpecUnit);
    }];
}

/**
 * 显示膏方数量选择弹窗（1、2、3勺）
 */
- (void)showCreamFormulaUnitCountSelection {
    if (!self.currentCreamFormulaSpecUnit || self.currentCreamFormulaSpecGramPerUnit <= 0) {
        return;
    }
    
    NSArray *options = @[@"1", @"2", @"3"];
    NSMutableArray *displayOptions = [NSMutableArray array];
    
    for (NSString *count in options) {
        double totalGram = [count integerValue] * self.currentCreamFormulaSpecGramPerUnit;
        NSString *displayText = [NSString stringWithFormat:@"%@%@(%.1f%@)", 
                                count, self.currentCreamFormulaSpecUnit, totalGram, self.currentCreamFormulaSpecWeightUnit];
        [displayOptions addObject:displayText];
    }
    
    [self showUnitSelectionWithOptions:displayOptions completion:^(NSInteger selectedIndex) {
        self.selectedCreamFormulaUnitCount = [options[selectedIndex] integerValue];
        [self updateCreamFormulaDosageDisplay];
        [self updateCreamFormulaGramDisplay];
        NSLog(@"选择膏方数量: %ld%@", (long)self.selectedCreamFormulaUnitCount, self.currentCreamFormulaSpecUnit);
    }];
}

/**
 * 通用的单位选择弹窗显示方法
 */
- (void)showUnitSelectionWithOptions:(NSArray *)options completion:(void(^)(NSInteger selectedIndex))completion {
    BRActionSheetView *actionSheet = [[BRActionSheetView alloc] init];
    actionSheet.title = @"选择用量";
    actionSheet.buttons = options;
    
    // 设置底部"自定义"按钮
    actionSheet.bottomTitle = @"自定义";
    
    // 设置选项按钮点击回调
    actionSheet.clickActionButtonCallBack = ^(NSInteger index) {
        if (completion) {
            completion(index);
        }
        [actionSheet close];
    };
    
    // 设置底部"自定义"按钮点击回调
    actionSheet.clickBottomButtonCallBack = ^{
        [actionSheet close];
        [self showCustomInputDialog];
    };
    
    [actionSheet show];
}

/**
 * 查找当前视图的视图控制器
 */
- (UIViewController *)findViewController {
    UIResponder *nextResponder = self;
    while (nextResponder) {
        nextResponder = [nextResponder nextResponder];
        if ([nextResponder isKindOfClass:[UIViewController class]]) {
            return (UIViewController *)nextResponder;
        }
    }
    return nil;
}

/**
 * 显示自定义输入弹窗
 */
- (void)showCustomInputDialog {
    // 根据当前剂型确定规格信息
    NSString *specUnit = @"";
    double gramPerUnit = 0.0;
    NSString *weightUnit = @"";
    NSString *title = @"自定义用量";
    
    if ([self.drugForm isEqualToString:@"蜜丸"]) {
        specUnit = self.currentSpecUnit ?: @"丸";
        gramPerUnit = self.currentSpecGramPerUnit;
        weightUnit = self.currentSpecWeightUnit ?: @"g";
        title = [NSString stringWithFormat:@"自定义用量 (规格: %.1f%@/%@)", gramPerUnit, weightUnit, specUnit];
    } else if ([self.drugForm isEqualToString:@"胶囊"]) {
        specUnit = self.currentCapsuleSpecUnit ?: @"粒";
        gramPerUnit = self.currentCapsuleSpecGramPerUnit;
        weightUnit = self.currentCapsuleSpecWeightUnit ?: @"g";
        title = [NSString stringWithFormat:@"自定义用量 (规格: %.1f%@/%@)", gramPerUnit, weightUnit, specUnit];
    } else if ([self.drugForm isEqualToString:@"水丸"]) {
        specUnit = self.currentWaterPillSpecUnit ?: @"丸";
        gramPerUnit = self.currentWaterPillSpecGramPerUnit;
        weightUnit = self.currentWaterPillSpecWeightUnit ?: @"g";
        title = [NSString stringWithFormat:@"自定义用量 (规格: %.1f%@/%@)", gramPerUnit, weightUnit, specUnit];
    } else if ([self.drugForm isEqualToString:@"膏方"]) {
        if (self.isCreamFormulaBottlePackage) {
            // 瓶装膏方：统一使用"g"作为单位
            specUnit = @"g";
            gramPerUnit = 1.0; // 瓶装膏方直接输入重量，每1g就是1g
            weightUnit = @"g";
            title = [NSString stringWithFormat:@"自定义用量 (规格: %@/%@)", self.currentCreamFormulaSpecWeightUnit, self.currentCreamFormulaSpecUnit];
        } else {
            specUnit = self.currentCreamFormulaSpecUnit ?: @"勺";
            gramPerUnit = self.currentCreamFormulaSpecGramPerUnit;
            weightUnit = self.currentCreamFormulaSpecWeightUnit ?: @"g";
            title = [NSString stringWithFormat:@"自定义用量 (规格: %.1f%@/%@)", gramPerUnit, weightUnit, specUnit];
        }
    }
    
    // 使用自定义弹窗，模仿Android版本的布局和交互
    [self showCustomDosageDialogWithTitle:title 
                                 specUnit:specUnit 
                              gramPerUnit:gramPerUnit 
                               weightUnit:weightUnit];
}

/**
 * 显示自定义用量弹窗（模仿Android版本的布局和交互）
 */
- (void)showCustomDosageDialogWithTitle:(NSString *)title
                               specUnit:(NSString *)specUnit
                            gramPerUnit:(double)gramPerUnit
                             weightUnit:(NSString *)weightUnit {
    
    // 创建弹窗背景
    UIView *backgroundView = [[UIView alloc] initWithFrame:[UIScreen mainScreen].bounds];
    backgroundView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.6];
    backgroundView.alpha = 0;
    
    // 创建弹窗容器
    UIView *dialogContainer = [[UIView alloc] init];
    dialogContainer.backgroundColor = [UIColor whiteColor];
    dialogContainer.layer.cornerRadius = 8;
    dialogContainer.layer.masksToBounds = YES;
    [backgroundView addSubview:dialogContainer];
    
    // 设置弹窗大小和位置
    CGFloat dialogWidth = 314;
    CGFloat dialogHeight = 200;
    dialogContainer.frame = CGRectMake((SCREEN_WIDTH - dialogWidth) / 2, 
                                     (kScreenHeight - dialogHeight) / 2, 
                                     dialogWidth, dialogHeight);
    
    // 标题
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = title;
    titleLabel.font = [UIFont systemFontOfSize:18];
    titleLabel.textColor = [UIColor colorWithRed:51/255.0 green:51/255.0 blue:51/255.0 alpha:1.0];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.numberOfLines = 0;
    [dialogContainer addSubview:titleLabel];
    
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@23);
        make.left.equalTo(@15);
        make.right.equalTo(@(-15));
    }];
    
    // 输入区域容器 - 整体横向居中
    UIView *inputContainer = [[UIView alloc] init];
    [dialogContainer addSubview:inputContainer];
    
    [inputContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(titleLabel.mas_bottom).offset(20);
        make.centerX.equalTo(dialogContainer); // 横向居中
        make.height.equalTo(@40);
    }];
    
    // "每次"标签
    UILabel *prefixLabel = [[UILabel alloc] init];
    prefixLabel.text = @"每次";
    prefixLabel.font = [UIFont systemFontOfSize:15];
    prefixLabel.textColor = [UIColor blackColor];
    [inputContainer addSubview:prefixLabel];
    
    [prefixLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@0);
        make.centerY.equalTo(inputContainer);
    }];
    
    // 输入框
    UITextField *inputTextField = [[UITextField alloc] init];
    inputTextField.backgroundColor = [UIColor colorWithRed:246/255.0 green:246/255.0 blue:246/255.0 alpha:1.0];
    inputTextField.layer.cornerRadius = 5;
    inputTextField.layer.masksToBounds = YES;
    inputTextField.font = [UIFont systemFontOfSize:16];
    inputTextField.textAlignment = NSTextAlignmentCenter;
    inputTextField.keyboardType = UIKeyboardTypeNumberPad;
    inputTextField.textColor = [UIColor colorWithRed:51/255.0 green:51/255.0 blue:51/255.0 alpha:1.0];
    [inputContainer addSubview:inputTextField];
    
    [inputTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(prefixLabel.mas_right).offset(10);
        make.centerY.equalTo(inputContainer);
        make.width.equalTo(@80);
        make.height.equalTo(@40);
    }];
    
    // 单位标签
    UILabel *unitLabel = [[UILabel alloc] init];
    unitLabel.text = specUnit;
    unitLabel.font = [UIFont systemFontOfSize:15];
    unitLabel.textColor = [UIColor blackColor];
    [inputContainer addSubview:unitLabel];
    
    [unitLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(inputTextField.mas_right).offset(10);
        make.centerY.equalTo(inputContainer);
    }];
    
    // 重量显示标签
    UILabel *weightLabel = [[UILabel alloc] init];
    weightLabel.text = @"(0g)";
    weightLabel.font = [UIFont systemFontOfSize:14];
    weightLabel.textColor = [UIColor colorWithRed:239/255.0 green:77/255.0 blue:59/255.0 alpha:1.0]; // 红色
    [inputContainer addSubview:weightLabel];
    
    [weightLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(unitLabel.mas_right).offset(5);
        make.centerY.equalTo(inputContainer);
        make.right.equalTo(@0); // 设置右边界，确保容器宽度适应内容
    }];
    
    // 按钮容器
    UIView *buttonContainer = [[UIView alloc] init];
    [dialogContainer addSubview:buttonContainer];
    
    [buttonContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(@(-37));
        make.left.equalTo(@25);
        make.right.equalTo(@(-25));
        make.height.equalTo(@40);
    }];
    
    // 取消按钮
    UIButton *cancelButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [cancelButton setTitle:@"取消" forState:UIControlStateNormal];
    [cancelButton setTitleColor:[UIColor colorWithRed:51/255.0 green:51/255.0 blue:51/255.0 alpha:1.0] forState:UIControlStateNormal];
    cancelButton.titleLabel.font = [UIFont systemFontOfSize:15];
    cancelButton.backgroundColor = [UIColor colorWithRed:235/255.0 green:235/255.0 blue:235/255.0 alpha:1.0];
    cancelButton.layer.cornerRadius = 5;
    cancelButton.layer.masksToBounds = YES;
    [buttonContainer addSubview:cancelButton];
    
    [cancelButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@0);
        make.top.bottom.equalTo(@0);
        make.width.equalTo(@118);
    }];
    
    // 保存按钮
    UIButton *saveButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [saveButton setTitle:@"保存" forState:UIControlStateNormal];
    [saveButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [saveButton setTitleColor:[UIColor colorWithWhite:1.0 alpha:0.5] forState:UIControlStateDisabled];
    saveButton.titleLabel.font = [UIFont systemFontOfSize:15];
    saveButton.backgroundColor = [[UIColor br_textBlueColor] colorWithAlphaComponent:0.3]; // 初始禁用状态使用半透明蓝色
    saveButton.layer.cornerRadius = 5;
    saveButton.layer.masksToBounds = YES;
    saveButton.enabled = NO; // 初始禁用
    [buttonContainer addSubview:saveButton];
    
    [saveButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@0);
        make.top.bottom.equalTo(@0);
        make.width.equalTo(@118);
    }];
    
    // 输入框变化监听
    [inputTextField addTarget:self action:@selector(customDialogTextFieldDidChange:) forControlEvents:UIControlEventEditingChanged];
    
    // 存储相关数据到输入框的关联对象中
    objc_setAssociatedObject(inputTextField, "weightLabel", weightLabel, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    objc_setAssociatedObject(inputTextField, "saveButton", saveButton, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    objc_setAssociatedObject(inputTextField, "gramPerUnit", @(gramPerUnit), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    objc_setAssociatedObject(inputTextField, "weightUnit", weightUnit, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    objc_setAssociatedObject(inputTextField, "specUnit", specUnit, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    objc_setAssociatedObject(inputTextField, "backgroundView", backgroundView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    // 取消按钮点击事件
    [cancelButton addTarget:self action:@selector(customDialogCancelButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    objc_setAssociatedObject(cancelButton, "backgroundView", backgroundView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    // 保存按钮点击事件
    [saveButton addTarget:self action:@selector(customDialogSaveButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    objc_setAssociatedObject(saveButton, "inputTextField", inputTextField, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    objc_setAssociatedObject(saveButton, "backgroundView", backgroundView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    // 显示弹窗
    AppDelegate *app = (AppDelegate *)[UIApplication sharedApplication].delegate;
    [app.window addSubview:backgroundView];
    
    [UIView animateWithDuration:0.3 animations:^{
        backgroundView.alpha = 1.0;
    }];
    
    // 自动弹出键盘
    [inputTextField becomeFirstResponder];
}

/**
 * 自定义弹窗输入框变化处理
 */
- (void)customDialogTextFieldDidChange:(UITextField *)textField {
    UILabel *weightLabel = objc_getAssociatedObject(textField, "weightLabel");
    UIButton *saveButton = objc_getAssociatedObject(textField, "saveButton");
    NSNumber *gramPerUnitNumber = objc_getAssociatedObject(textField, "gramPerUnit");
    NSString *weightUnit = objc_getAssociatedObject(textField, "weightUnit");
    
    double gramPerUnit = [gramPerUnitNumber doubleValue];
    NSString *inputText = textField.text;
    
    // 限制输入长度为3位数字
    if (inputText.length > 3) {
        textField.text = [inputText substringToIndex:3];
        inputText = textField.text;
    }
    
    // 只允许数字输入
    NSCharacterSet *nonDigitSet = [[NSCharacterSet decimalDigitCharacterSet] invertedSet];
    if ([inputText rangeOfCharacterFromSet:nonDigitSet].location != NSNotFound) {
        // 移除非数字字符
        textField.text = [[inputText componentsSeparatedByCharactersInSet:nonDigitSet] componentsJoinedByString:@""];
        inputText = textField.text;
    }
    
    // 更新重量显示
    if (inputText.length > 0) {
        int unitCount = [inputText intValue];
        if (unitCount > 0) {
            double totalWeight = unitCount * gramPerUnit;
            weightLabel.text = [NSString stringWithFormat:@"(%.1f%@)", totalWeight, weightUnit];
            saveButton.enabled = YES;
            saveButton.backgroundColor = [UIColor br_textBlueColor]; // 启用时使用项目蓝色
        } else {
            weightLabel.text = [NSString stringWithFormat:@"(0%@)", weightUnit];
            saveButton.enabled = NO;
            saveButton.backgroundColor = [[UIColor br_textBlueColor] colorWithAlphaComponent:0.3]; // 禁用时使用半透明蓝色
        }
    } else {
        weightLabel.text = [NSString stringWithFormat:@"(0%@)", weightUnit];
        saveButton.enabled = NO;
        saveButton.backgroundColor = [[UIColor br_textBlueColor] colorWithAlphaComponent:0.3]; // 禁用时使用半透明蓝色
    }
}

/**
 * 自定义弹窗取消按钮点击
 */
- (void)customDialogCancelButtonTapped:(UIButton *)button {
    UIView *backgroundView = objc_getAssociatedObject(button, "backgroundView");
    [self dismissCustomDialog:backgroundView];
}

/**
 * 自定义弹窗保存按钮点击
 */
- (void)customDialogSaveButtonTapped:(UIButton *)button {
    UITextField *inputTextField = objc_getAssociatedObject(button, "inputTextField");
    UIView *backgroundView = objc_getAssociatedObject(button, "backgroundView");
    NSString *specUnit = objc_getAssociatedObject(inputTextField, "specUnit");
    
    NSString *input = inputTextField.text;
    if (input.length > 0) {
        double inputValue = [input doubleValue];
        if (inputValue > 0) {
            // 根据剂型更新对应的选择数量和显示
            if ([self.drugForm isEqualToString:@"蜜丸"]) {
                self.selectedUnitCount = (NSInteger)inputValue;
                [self updatePillDosageDisplay];
                [self updatePillGramDisplay];
            } else if ([self.drugForm isEqualToString:@"胶囊"]) {
                self.selectedCapsuleUnitCount = (NSInteger)inputValue;
                [self updateCapsuleDosageDisplay];
                [self updateCapsuleGramDisplay];
            } else if ([self.drugForm isEqualToString:@"水丸"]) {
                self.selectedWaterPillUnitCount = (NSInteger)inputValue;
                [self updateWaterPillDosageDisplay];
                [self updateWaterPillGramDisplay];
            } else if ([self.drugForm isEqualToString:@"膏方"]) {
                if (self.isCreamFormulaBottlePackage) {
                    // 瓶装膏方：直接设置重量数值，用户输入的就是克数
                    self.creamFormulaDirectWeightValue = inputValue;
                } else {
                    // 非瓶装膏方：设置包装数量
                    self.selectedCreamFormulaUnitCount = (NSInteger)inputValue;
                }
                [self updateCreamFormulaDosageDisplay];
                [self updateCreamFormulaGramDisplay];
            }
            NSLog(@"自定义%@数量: %.1f%@", self.drugForm, inputValue, specUnit);
        }
    }
    
    [self dismissCustomDialog:backgroundView];
}

/**
 * 关闭自定义弹窗
 */
- (void)dismissCustomDialog:(UIView *)backgroundView {
    [UIView animateWithDuration:0.3 animations:^{
        backgroundView.alpha = 0;
    } completion:^(BOOL finished) {
        [backgroundView removeFromSuperview];
    }];
}

#pragma mark - 辅料相关方法

/**
 * 判断指定剂型是否应该显示辅料行
 * @param drugForm 剂型名称
 * @return 是否应该显示辅料行
 */
- (BOOL)shouldShowAuxiliaryMaterialRowForDrugForm:(NSString *)drugForm {
    if (!drugForm || drugForm.length == 0) {
        return NO;
    }
    
    // 仅在膏方剂型时显示辅料行
    // 注意：不检查auxiliaryMaterialList是否有数据，因为在setDrugForm时辅料数据可能还没有设置
    // 辅料数据的有无会在updateAuxiliaryMaterialWithFactoryModel方法中处理显示/隐藏逻辑
    return [drugForm isEqualToString:@"膏方"];
}

/**
 * 更新辅料显示文本
 */
- (void)updateAuxiliaryMaterialDisplayText:(NSString *)displayText {
    if (self.auxiliaryMaterialPlaceholderLabel) {
        self.auxiliaryMaterialPlaceholderLabel.text = displayText;
        
        // 根据是否选择了辅料来设置颜色
        if (displayText && ![displayText isEqualToString:@"请选择辅料"]) {
            self.auxiliaryMaterialPlaceholderLabel.textColor = [UIColor br_mainBlueColor]; // 蓝色
        } else {
            self.auxiliaryMaterialPlaceholderLabel.textColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1.0]; // 灰色
        }
    }
}

/**
 * 辅料行点击事件处理
 */
- (void)auxiliaryMaterialRowTapped:(UITapGestureRecognizer *)gesture {
    if (self.auxiliaryMaterialSelectionBlock) {
        self.auxiliaryMaterialSelectionBlock();
    }
}

/**
 * 更新厂商模型并初始化辅料数据
 * @param factoryModel 厂商模型
 */
- (void)updateAuxiliaryMaterialWithFactoryModel:(BRSubFactoryModel *)factoryModel {
    self.auxiliaryMaterialList = factoryModel.makeMaterialList;
    
    // 重置选中状态
    [self.selectedAuxiliaryMaterials removeAllObjects];
    self.isNoAuxiliaryMaterial = NO;
    
    // 根据剂型判断是否应该显示辅料行
    BOOL shouldShow = [self shouldShowAuxiliaryMaterialRowForDrugForm:self.drugForm];
    
    // 如果应该显示辅料行但没有辅料数据，则隐藏辅料行
    if (shouldShow && (!self.auxiliaryMaterialList || self.auxiliaryMaterialList.count == 0)) {
        shouldShow = NO;
    }
    
    [self setAuxiliaryMaterialRowHidden:!shouldShow];
    
    if (shouldShow) {
        // 当剂型为膏方，并且用法用量中有辅料的时候，默认选择辅料选项中的第一个
        if ([self.drugForm isEqualToString:@"膏方"] && self.auxiliaryMaterialList.count > 0) {
            // 分离辅料选项和"不添加辅料"选项
            NSMutableArray *materialOptions = [NSMutableArray array];
            for (NSString *option in self.auxiliaryMaterialList) {
                if (![option isEqualToString:@"不添加辅料"]) {
                    [materialOptions addObject:option];
                }
            }

            // 如果有辅料选项，默认选择第一个
            if (materialOptions.count > 0) {
                NSString *firstMaterial = materialOptions.firstObject;
                [self.selectedAuxiliaryMaterials addObject:firstMaterial];
                [self updateAuxiliaryMaterialDisplayText:firstMaterial];
                NSLog(@"膏方剂型默认选择第一个辅料: %@", firstMaterial);
            } else {
                [self updateAuxiliaryMaterialDisplayText:@"请选择辅料"];
            }
        } else {
            [self updateAuxiliaryMaterialDisplayText:@"请选择辅料"];
        }
    }

    NSLog(@"更新辅料信息 - 剂型: %@, 辅料数量: %lu, 显示辅料行: %@",
          self.drugForm,
          (unsigned long)(self.auxiliaryMaterialList ? self.auxiliaryMaterialList.count : 0),
          shouldShow ? @"是" : @"否");
}

@end
