//
//  PrescriptionViewController.m
//  BRZY
//  用药界面
//  Created by <PERSON><PERSON><PERSON> on 2017/11/15.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "PrescriptionViewController.h"
#import "AddDrugViewController.h"
#import "MedicatedInfoViewController.h"
#import "FactoryInfoViewController.h"
#import <objc/runtime.h>

#import "BRPrescriptionPatientView.h"
#import "BRPrescriptionDiagnosesView.h"
#import "BRPrescriptionDisplayView.h"
#import "BRPresUsageView.h"
#import "BRPresOtherView.h"
#import "BRPresNoteView.h"
#import "BRPrescriptionChargeView.h"
#import "BRPresOfflineView.h"
#import "BRActionSheetView.h"
#import "BRPresContraindicationView.h"
#import "BRPresAddPatientView.h"
#import "BRPresSelectTypeView.h"
#import "BRAlertView.h"

#import "BRPrescriptionModel.h"
#import "BRFactoryModel.h"
#import "BRSubFactoryModel.h"
#import "BRSubMedicineModel.h"
#import "BRContraindicationModel.h"

#import "IMDataBaseManager.h"
#import "BRTemporaryPrescription.h"

#import "BRWritePatientInfoView.h"
#import "BRUpdateUserInfoModel.h"

#define VIEW_VERTICAL_MARGIN  10

@interface PrescriptionViewController ()<UIScrollViewDelegate, BRPrescriptionDiagnosesDelegate, BRPrescriptionOtherDelegate, BRPrescriptionNoteDelegate, AddDrugVCDelegate>

@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *containerBgView;

@property (nonatomic, strong) BRPrescriptionPatientView *patientView;//显示患者信息的view
@property (nonatomic, strong) BRPrescriptionDiagnosesView *diagnosesView;//辨证的view
@property (nonatomic, strong) BRPrescriptionDisplayView *displayView;//用来显示用药的view
@property (nonatomic, strong) BRPresUsageView *usageView;//用法用量的view
@property (nonatomic, strong) BRPresOtherView *otherView;//其它的view
@property (nonatomic, strong) BRPresNoteView *noteView;//按语的view
@property (nonatomic, strong) BRPrescriptionChargeView *chargeView;//显示金额的view
@property (nonatomic, strong) BRPresOfflineView *offlineView;//线下诊断的view
@property (nonatomic, strong) UIButton *createButton;//生成医案
@property (nonatomic, strong) BRActionSheetView *actionSheetView;
@property (nonatomic, strong) BRPresAddPatientView *addPatientView;//添加患者的view
@property (nonatomic, strong) BRActionSheetView *timeActionSheet;//选择用药时间弹出框
@property (nonatomic, strong) BRPresContraindicationView *contraindicationView;//服药禁忌弹出框
@property (nonatomic, strong) BRPresSelectTypeView *selectTypeView;//选择厂商弹出框
@property (nonatomic, strong) BRActionSheetView *decoctionPreferenceActionSheet; //代煎偏好弹窗
@property (nonatomic, strong) BRActionSheetView *specificationActionSheet; //规格选择弹窗

//快速开方填写用户信息
@property (nonatomic, strong) BRWritePatientInfoView *writePatientInfoView;

@property (nonatomic, strong) NSMutableArray *patientArray;//所有患者
@property (nonatomic, strong) NSMutableArray *medicationTimeArr;//所有用药时间
@property (nonatomic, strong) NSMutableArray *contraindicationArr;//服药禁忌
@property (nonatomic, strong) NSMutableArray *factoryArr;//药厂信息
@property (nonatomic, strong) NSMutableArray *drugArray;//处方中包含的药材
@property (nonatomic, assign) NSInteger masterSelectedIndex;//选择剂型 颗粒、饮片、膏方等左边一列的index
@property (nonatomic, assign) NSInteger detailSelectedIndex;//选择剂型 比如颗粒下面的华润三九、康仁堂等右边一列的index
@property (nonatomic, strong) NSString *drugPrice;//后台算回来的药费
@property (nonatomic, strong) NSString *makeCost;//制作费
@property (nonatomic, strong) NSString *makeDays;//制作天数
@property (nonatomic, strong) NSString *balanceWeight;//药材总重
@property (nonatomic, strong) NSString *changedCharge;//诊费
@property (nonatomic, strong) NSString *defaultCharge;//默认诊费
@property (nonatomic, strong) NSString *serviceProportion;//医技服务费比例
@property (nonatomic, strong) NSString *defaultServiceCharge;//药费根据默认的医技服务费比例算出来的医技服务费
@property (nonatomic, strong) NSString *defaultServiceProportion;//默认医技服务费比例
@property (nonatomic, assign) BOOL isShowDetail;//药费明细是否在显示
@property (nonatomic, strong) NSDecimalNumber *totalPriceDecimal;//总计

@property (nonatomic, strong) BRTemporaryPrescription *temPresModel;//临时药方
@property (nonatomic, strong) NSString *primaryKey;//临时药方表的主键
@property (nonatomic, assign) NSInteger generatClickNumber;//点击生成医案按钮的次数

@property (nonatomic, assign) BOOL isUsePharmacyMake;

@property (nonatomic, copy) NSString *currentDrugForm;

//填写的患者信息
@property (nonatomic, copy) NSString *patientName;
@property (nonatomic, copy) NSString *patientAge;
@property (nonatomic, copy) NSString *patientSex;
@property (nonatomic, copy) NSString *patient_is_pregnanet;

@property (nonatomic, assign) BOOL isPregnantSet;

//快速开方临时id
@property (nonatomic, copy) NSString *quickOrderTempId;

//代煎偏好数组
@property (nonatomic, strong) NSArray *decoctionPreferenceArray;
//当前选择代煎偏好索引
@property (nonatomic, assign) NSInteger decoctionPreferenceIndex;
//当前选择的代煎偏好字符串
@property (nonatomic, strong) NSString *selectedDecoctionPreference;
//当前代煎的packageSpecList数据
@property (nonatomic, strong) NSArray<BRPackageSpecModel *> *currentDecoctionPackageSpecList;

@end

@implementation PrescriptionViewController

- (void)viewWillAppear:(BOOL)animated {
    
    [super viewWillAppear:animated];
    
    //添加监听APP即将结束进程的通知
//    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(UIApplicationWillTerminateNotification:) name:UIApplicationWillTerminateNotification object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(UIApplicationWillTerminateNotification:) name:UIApplicationWillResignActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updatePatientInfo:) name:kIMNotificationUPdatePatientInfo object:nil];
    
}

-(void)viewDidDisappear:(BOOL)animated {
    
    [super viewDidDisappear:animated];
    //移除监听APP结束进程的通知
//    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationWillTerminateNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationWillResignActiveNotification object:nil];
    
}

- (void)UIApplicationWillTerminateNotification:(NSNotification *)noti {
    
    //开始存储药方
    [self saveTemporaryPrescription];
    
}

- (void)updatePatientInfo:(NSNotification *)noti {
    
    NSDictionary *patientInfo = noti.userInfo;
    
    if (_patientArray.count == 0) {
        return;
    }
    
    NSString *userId = [patientInfo objectForKey:@"userId"];
    NSString *takerId = [patientInfo objectForKey:@"takerId"];
    
    //先判断是不是这个人的,如果不是不修改
    if (![_patientId isEqualToString:userId]) {
        return;
    }
    
    //再判断是不是这个子患者，不是这个子患者不更新
    if (![_patientModel.patientId isEqualToString:takerId]) {
        return;
    }
    
    _patientModel.patientId = takerId;
    _patientModel.age = [patientInfo objectForKey:@"age"];
    _patientModel.sex = [patientInfo objectForKey:@"sex"];
    _patientModel.isPregnant = [patientInfo objectForKey:@"isPregnent"];
    _patientModel.name = [patientInfo objectForKey:@"name"];
    
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    //初始化用药界面的一些参数
    _generatClickNumber = 0;
    _masterSelectedIndex = 0;
    _detailSelectedIndex = 0;
    _drugPrice = @"0";
    _changedCharge = @"0";
    _makeCost = @"0";
    _isShowDetail = NO;
    _balanceWeight = @"0";
    _totalPriceDecimal = [NSDecimalNumber decimalNumberWithString:@"0"];
    
    _patientArray = [[NSMutableArray alloc]initWithCapacity:0];
    _medicationTimeArr = [[NSMutableArray alloc]initWithCapacity:0];
    _contraindicationArr = [[NSMutableArray alloc]initWithCapacity:0];
    _factoryArr = [[NSMutableArray alloc]initWithCapacity:0];
    _drugArray = [[NSMutableArray alloc]initWithCapacity:0];
    
    _temPresModel = [[BRTemporaryPrescription alloc]init];
    
    _isUsePharmacyMake = YES;
    _isPregnantSet = NO;
    
    //代煎偏好数组 - 现在从packageSpecList动态获取
    _decoctionPreferenceArray = @[];
    //当前代煎索引
    _decoctionPreferenceIndex = 0;
    //初始化代煎偏好相关属性
    _selectedDecoctionPreference = @"";
    _currentDecoctionPackageSpecList = @[];
    
    //清空用药方法选项记录
    [Utils saveUsageType:kUsageMethodKey text:@"" withPatientId:self.patientId];
    
    //获取患者信息
    if (self.prescribeType == BRPrescribeTypeNormal) {
        [self getUserInfoWithType:BRGetPatientInfoTypeFirst];
        
        _quickOrderTempId = @"";
    }
    else{
        //设置快速开方默认用户是否怀孕
        self.patient_is_pregnanet = @"0";
        
        _quickOrderTempId = [NSString stringWithFormat:@"%.0f",[[NSDate date] timeIntervalSince1970]];
    }
    
    NSLog(@"quickOrderTempId == %@",self.quickOrderTempId);
    
    //获取厂商信息
    [self getFactoryInfoWithType:@"viewWillAppear"];
    
    //获取诊费
    [self getCharge];
    
    //创建UI部分
    [self configUI];
    
    if (self.prescribeType == BRPrescribeTypeQuick || self.prescribeType == BRPrescribeTypeMessage) {
        [self loadTemporayPrescription];
    }
    
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(syncPatientDocumentWithNotification:) name:kNotificationPatientDocumentChangePatient object:nil];
    //通知监听,微信用户修改了用户信息
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updateUserInfo:) name:kIMNotificationUPdatePatientInfo object:nil];
    
    
}

#pragma mark 加载未完成药方信息
- (void)loadTemporayPrescription {
    
    //获取医技服务费比例
    [self getServiceChargeProportion];
    
    if (_factoryArr.count == 0) {
        
        __weak PrescriptionViewController *weakSelf = self;
        
        
        NSMutableArray *drugIdsArr = [[NSMutableArray alloc]initWithCapacity:0];
        for (BRSubMedicineModel *model in _drugArray) {
            [drugIdsArr addObject:model.drugId];
        }
        NSString *drugIdsString = [drugIdsArr componentsJoinedByString:@","];
        
        //请求药厂数据
        NSDictionary *dict = @{@"method_code":@"000255",
                               @"drugIds": drugIdsString,
                               @"apiVer" : @"2"
                               };
        
        MBProgressHUD *hud = nil;
        hud = [Utils createLoadingHUD];
        
        [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
            
        } success:^(NSURLSessionDataTask *task, id responseObject) {
            
            [hud hideAnimated:YES];
            
            NSString *code = [responseObject objectForKey:@"code"];
            if ([code isEqualToString:@"0000"]) {
                
                weakSelf.factoryArr = [BRFactoryModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"list"]];
                
                for (NSInteger index=0; index<weakSelf.factoryArr.count; index++) {
                    
                    BRFactoryModel *facoryModel = [weakSelf.factoryArr objectAtIndex:index];
                    NSArray *factoryArr = [BRSubFactoryModel mj_objectArrayWithKeyValuesArray:facoryModel.list];
                    facoryModel.list = factoryArr;
                    
                    [weakSelf.factoryArr replaceObjectAtIndex:index withObject:facoryModel];
                    
                }
                
                [weakSelf loadTemporayPrescriptionFromDataBase];
                
            }
            else {
                
                NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
                [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
                
            }
            
            
        } failure:^(NSURLSessionDataTask *task, NSError *error) {
            
            [hud hideAnimated:YES];
            [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
        }];
        
    }
    else {
        [self loadTemporayPrescriptionFromDataBase];
        
    }
}

//从数据库中加载未完成药方信息
- (void)loadTemporayPrescriptionFromDataBase {

    if (self.prescribeType != BRPrescribeTypeNormal) {
        return;
    }
    //临时药方的主键是由登录用户的id以及患者的id组成的
    _primaryKey = [NSString stringWithFormat:@"%@%@",[UserManager shareInstance].getUserId,self.patientId];

    if (_temPresModel && _temPresModel.prescriptionStr) {
        return;
    }

    IMDataBaseManager *dbManager = [IMDataBaseManager shareInstance];
    _temPresModel = [dbManager getTemporaryPrescriptionWithPrimaryKey:_primaryKey];

    if (_temPresModel.prescriptionStr) {

        __weak PrescriptionViewController *weakSelf = self;

        //恢复未完成药方
        [self restoredToTemporaryPrescriptionWithTemPresModel:_temPresModel];

        /*
         //提示是否恢复现在未完成的样子
         BRAlertView *alertView = [[BRAlertView alloc] init];
         __weak BRAlertView *weakAlertView = alertView;
         [alertView.cancelButton setTitle:@"继续编辑" forState:UIControlStateNormal];
         [alertView.okButton setTitle:@"重新用药" forState:UIControlStateNormal];
         [alertView showAlertViewWithCancelButton:@"上次用药方案未完成，是否继续？" completion:^(BOOL isOk) {

         if (!isOk) {
         //继续编辑，跳转添加药材页面
         if (_factoryArr.count == 0) {
         [self getFactoryInfoWithType:@"restoredToTemporaryPrescription"];
         }
         else {
         [self getPrescriptionFactoryIndex];
         }

         [weakSelf editSelectedDrugsWithType:YES];
         }
         else {
         //重新用药，恢复到初始页面
         [[IMDataBaseManager shareInstance]deleteTemporaryPrescriptionWithPrimaryKey:_primaryKey];
         [weakSelf returnToTheOriginalConditionWithType:BRReturnToTheOriginalConditionTypeDefault];
         }

         [alertView close];

         }];
         */

    }

}

#pragma mark 检查快速开方临时处方
- (void)checkQuickPrescriptionWithPatientName:(NSString *)patientName {
    if (self.prescribeType != BRPrescribeTypeQuick && self.prescribeType != BRPrescribeTypeMessage) {
        return;
    }

    if (!patientName || patientName.length == 0) {
        return;
    }

    // 快速开方的主键是由登录用户的id以及患者姓名组成的
    NSString *quickPrimaryKey = [NSString stringWithFormat:@"quick_%@_%@", [UserManager shareInstance].getUserId, patientName];

    IMDataBaseManager *dbManager = [IMDataBaseManager shareInstance];
    BRTemporaryPrescription *quickTemPresModel = [dbManager getTemporaryPrescriptionWithPrimaryKey:quickPrimaryKey];

    if (quickTemPresModel && quickTemPresModel.prescriptionStr) {
        __weak PrescriptionViewController *weakSelf = self;

        // 提示是否恢复现在未完成的样子
        BRAlertView *alertView = [[BRAlertView alloc] init];
        alertView.isHideWhenTapBackground = YES;
        [alertView.cancelButton setTitle:@"继续编辑" forState:UIControlStateNormal];
        [alertView.okButton setTitle:@"重新用药" forState:UIControlStateNormal];

        NSString *alertMessage = [NSString stringWithFormat:@"%@上次用药方案未完成，是否继续？", patientName];
        [alertView showAlertViewWithCancelButton:alertMessage completion:^(BOOL isOk) {

            if (!isOk) {
                // 继续编辑，恢复临时处方
                weakSelf.temPresModel = quickTemPresModel;
                weakSelf.primaryKey = quickPrimaryKey;
                [weakSelf restoredToTemporaryPrescriptionWithTemPresModel:quickTemPresModel];

                // 如果需要跳转添加药材页面
                if (weakSelf.factoryArr.count == 0) {
                    [weakSelf getFactoryInfoWithType:@"restoredToTemporaryPrescription"];
                } else {
                    [weakSelf getPrescriptionFactoryIndex];
                }
            } else {
                // 重新用药，删除临时处方
                [[IMDataBaseManager shareInstance] deleteTemporaryPrescriptionWithPrimaryKey:quickPrimaryKey];
                [weakSelf returnToTheOriginalConditionWithType:BRReturnToTheOriginalConditionTypeDefault];
            }
        }];
    }
}

#pragma mark 获取患者信息
- (void)getUserInfoWithType:(BRGetPatientInfoType)type {
    
    //获取所有患者信息
    NSDictionary *dict = @{@"method_code":@"000232",
                           @"patientId":self.patientId
                           };
    
    __weak PrescriptionViewController *weakSelf = self;
    
    MBProgressHUD *hud = nil;
    //如果是第一次请求那么现实loading
    if (type == BRGetPatientInfoTypeNotFirst) {
        hud = [Utils createLoadingHUDWithTitle:@""];
    }
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if (hud) {
            hud.hidden = YES;
        }
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            
            weakSelf.patientArray = [BRPatientModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"patients"]];
            
            if (type == BRGetPatientInfoTypeFirst) {
                
                //如果患者资料不为空那么不赋值
                if (!_patientModel.patientId) {
                    [weakSelf setPatientInfoWithPatientIndex:0];
                }
                
                if (self.subPatientModel) {
                    [self changeSubPatientModel:self.subPatientModel];
                }
            }
            else {
                [weakSelf showSelectPatientsViewWithPatientsArr:weakSelf.patientArray];
            }
            
        }
        else {
            
            NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
            [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
            
        }
        
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
        if (hud) {
            hud.hidden = YES;
        }
        
        [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
        
    }];
    
}

#pragma mark-获取诊费
- (void)getCharge {
    
    //获取诊费
    NSDictionary *dict = @{@"method_code":@"000256"};
    
    __weak PrescriptionViewController *weakSelf = self;
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        //如果未完成处方中已经修改诊费了，那么默认诊费不是按照请求回来的
        NSString *primaryKey = [NSString stringWithFormat:@"%@%@",[UserManager shareInstance].getUserId,weakSelf.patientId];
        IMDataBaseManager *dbManager = [IMDataBaseManager shareInstance];
        BRTemporaryPrescription *temPresModel = [dbManager getTemporaryPrescriptionWithPrimaryKey:primaryKey];
        NSString *prescriptionStr = temPresModel.prescriptionStr;
        NSDictionary *presDict = [prescriptionStr mj_JSONObject];
        NSString *consultationfee = [presDict objectForKey:@"consultationfee"];
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
        
            NSString *priceStr = [responseObject objectForKey:@"changeSetPrice"];
            if (priceStr.length == 0) {
                priceStr = @"0";
            }
            
            weakSelf.defaultCharge = priceStr;
            if (consultationfee.length != 0) {
                return ;
            }
            
            weakSelf.chargeView.chargeTextField.text = priceStr;
            weakSelf.changedCharge = priceStr;
            
            NSDecimalNumberHandler*roundingBehavior = [NSDecimalNumberHandler decimalNumberHandlerWithRoundingMode:NSRoundUp scale:2 raiseOnExactness:NO raiseOnOverflow:NO raiseOnUnderflow:NO raiseOnDivideByZero:YES];
            
            //获取完诊费，计算总费用
            NSDecimalNumber *chargeDecimal = [NSDecimalNumber decimalNumberWithString:priceStr];
            chargeDecimal = [chargeDecimal decimalNumberByMultiplyingBy:[NSDecimalNumber decimalNumberWithString:@"1.064"]];
            weakSelf.totalPriceDecimal = [weakSelf.totalPriceDecimal decimalNumberByAdding:chargeDecimal withBehavior:roundingBehavior];
            [weakSelf setTotalPriceWithTotalPriceDecimal:weakSelf.totalPriceDecimal];
            
            
        }
        else {
            
            NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
            [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
            
        }
        
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
        [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
        
    }];
    
}

#pragma mark -  通知处理
//监听微信用户是否修改了用户信息
- (void)updateUserInfo:(NSNotification *)notify {
    NSDictionary *dict = [NSDictionary dictionaryWithDictionary:notify.userInfo];
    BRUpdateUserInfoModel *userInfoModel = [BRUpdateUserInfoModel mj_objectWithKeyValues:dict];
    
    // 判断是否为当前患者
    if (![self.patientId isEqualToString:userInfoModel.userId]) {
        return;
    }
    
    // 查找并更新患者数组中的信息
    for (NSInteger i = 0; i < self.patientArray.count; i++) {
        BRPatientModel *patientModel = [self.patientArray objectAtIndex:i];
        
        // 找到对应的患者
        if ([patientModel.patientId isEqualToString:userInfoModel.takerId]) {
            // 更新患者信息
            patientModel.age = userInfoModel.age;
            patientModel.name = userInfoModel.name;
            patientModel.sex = [NSString stringWithFormat:@"%d", userInfoModel.sex];
            patientModel.isPregnant = [NSString stringWithFormat:@"%d", userInfoModel.isPregnent];
            
            // 如果是当前显示的患者，更新界面
            if ([self.patientModel.patientId isEqualToString:patientModel.patientId]) {
                // 检查怀孕状态是否改变，如果改变可能需要清空药品信息
                if (![self.patientModel.isPregnant isEqualToString:patientModel.isPregnant] &&
                    self.drugArray.count > 0) {
                    
                    BRAlertView *alertView = [[BRAlertView alloc] init];
                    alertView.isHideWhenTapBackground = YES;
                    __weak typeof(self) weakSelf = self;
                    
                    [alertView showAlertViewWithCancelButton:@"切换怀孕状态，已填用药信息将清空。"
                    completion:^(BOOL isOk) {
                        if (!isOk) {
                            return;
                        }
                        
                        // 清空用药信息并更新界面
                        [weakSelf returnToTheOriginalConditionWithType:BRReturnToTheOriginalConditionTypeDefault];
                        [weakSelf setPatientInfoWithPatientModel:patientModel isSync:YES];
                    }];
                } else {
                    // 直接更新界面显示
                    [self setPatientInfoWithPatientModel:patientModel isSync:YES];
                }
            }
            
            // 更新数组中的数据
            [self.patientArray replaceObjectAtIndex:i withObject:patientModel];
            break;
        }
    }
}

#pragma mark 指定患者
- (void)changeSubPatientModel:(BRPatientModel *)subPatientModel {
    
    NSInteger patientIndex = -1;
    for (NSInteger index=0; index<_patientArray.count; index++) {
        
        BRPatientModel *model = [_patientArray objectAtIndex:index];
        
        if ([subPatientModel.patientId isEqualToString:model.patientId]) {
            patientIndex = index;
        }
        
    }
    
    if (patientIndex == -1) {
        
        subPatientModel.isSelf = @"0";
        [_patientArray insertObject:subPatientModel atIndex:1];
        patientIndex = 1;
        
    }
    
    BRPatientModel *patientModel = [_patientArray objectAtIndex:patientIndex];
    if (patientModel.isPregnant.length == 0) {
        patientModel.isPregnant = @"0";
    }
    
    if (_patientModel.isPregnant.length == 0) {
        _patientModel.isPregnant = @"0";
    }
    
    if (![patientModel.isPregnant isEqualToString:_patientModel.isPregnant] && _drugArray.count > 0) {
        
        BRAlertView *alertView = [[BRAlertView alloc]init];
        alertView.isHideWhenTapBackground = YES;
        __weak BRAlertView *weakAlertView = alertView;
        [alertView showAlertViewWithCancelButton:@"切换怀孕状态，已填用药信息将清空。" completion:^(BOOL isOk) {
            
            if (!isOk) {
                return ;
            }
            else {
            
                //切换患者后还原用药为最初状态
                [self returnToTheOriginalConditionWithType:BRReturnToTheOriginalConditionTypeDefault];
                [self setPatientInfoWithPatientIndex:patientIndex];
                
            }
            
        }];
        
    }
    else {
        [self setPatientInfoWithPatientIndex:patientIndex];
    }

    
}

#pragma mark 获取医技服务费比例
- (void)getServiceChargeProportion {
    
    //获取诊费
    
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithDictionary:@{
        @"method_code":@"000311",
//        @"buyUserId":_patientId,
        @"apiVer":@"1",
        @"userId":[UserManager shareInstance].getUserId
    }];
    
    
    if (self.prescribeType == BRPrescribeTypeNormal) {
        [dict setValuesForKeysWithDictionary:@{
            @"buyUserId":_patientId,
        }];
    }
    
    __weak PrescriptionViewController *weakSelf = self;
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            
            weakSelf.defaultServiceProportion = [responseObject objectForKey:@"data"];
            weakSelf.serviceProportion = [responseObject objectForKey:@"data"];
            
        }
        else {
            
            NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
            [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
            
        }
        
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
        [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
        
    }];
    
}

#pragma mark - configUI
- (void)configUI {
    
    self.view.backgroundColor = [UIColor br_backgroundColor];
    
    __weak PrescriptionViewController *weakSelf = self;
    
    _scrollView = [[UIScrollView alloc]init];
    _scrollView.delegate = self;
    _scrollView.contentSize = CGSizeMake(SCREEN_WIDTH, 0);
    [self.view addSubview:_scrollView];
    
    [_scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
       
        make.top.and.left.and.right.equalTo(weakSelf.view);
//        make.bottom.equalTo(weakSelf.view).offset(-45);
        if (@available(iOS 11.0, *)) {
            make.bottom.equalTo(weakSelf.view.mas_safeAreaLayoutGuideBottom).with.offset(-45);
        } else {
            make.bottom.equalTo(weakSelf.view).offset(-45);
        }
        
    }];
    
    UIView *containerBgView = [UIView new];
    containerBgView.backgroundColor = [UIColor clearColor];
    [_scrollView addSubview:containerBgView];
    
    [containerBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(_scrollView);
        make.width.equalTo(_scrollView);
    }];
    
    self.containerBgView = containerBgView;
    
#pragma mark 患者信息
    
    //快速开方
    if (self.prescribeType == BRPrescribeTypeQuick || self.prescribeType == BRPrescribeTypeMessage) {
        
        _writePatientInfoView = [[BRWritePatientInfoView alloc] init];
        [self.containerBgView addSubview:_writePatientInfoView];
        
        [_writePatientInfoView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.equalTo(weakSelf.containerBgView);
            make.height.mas_greaterThanOrEqualTo(@100);
        }];
        
        if (self.takerInfoModel) {
            self.patientName = [self.takerInfoModel.name stringByTrim];
            self.patientSex = [self.takerInfoModel.sex isEqualToString:@"1"] ? @"1" : @"2";
            self.patient_is_pregnanet = self.takerInfoModel.isPregnant;
            self.patientAge = self.takerInfoModel.age;
        }
        
        __weak __typeof(self)weakSelf = self;
        _writePatientInfoView.patientRealNameValueChangeBlock = ^(NSString * _Nonnull text) {
            NSString *trimmedText = [text stringByTrim];
            weakSelf.patientName = trimmedText;

            // 检查是否有该患者的临时处方
            if (trimmedText.length > 0) {
                [weakSelf checkQuickPrescriptionWithPatientName:trimmedText];
            }
        };
        
        _writePatientInfoView.patientAgeValueChangeBlock = ^(NSString * _Nonnull text) {
            weakSelf.patientAge = text;
        };
        
        _writePatientInfoView.genderChooseBlock = ^(NSInteger genderIndex) {
            weakSelf.patientSex = [NSString stringWithFormat:@"%lu",genderIndex];
        };
        
        _writePatientInfoView.pregnantChangeBlock = ^(NSInteger pregnantIndex) {
            
            weakSelf.patient_is_pregnanet = [NSString stringWithFormat:@"%lu",pregnantIndex];
            //切换怀孕状态
            
            if (weakSelf.isPregnantSet && weakSelf.drugArray.count > 0) {
                
                BRAlertView *alertView = [[BRAlertView alloc] init];
                alertView.isHideWhenTapBackground = YES;
                [alertView showAlertViewWithCancelButton:@"切换怀孕状态，已填用药信息将清空。" completion:^(BOOL isOk) {
                    if (!isOk) {
                        return ;
                    }
                    else {
                        [weakSelf returnToTheOriginalConditionWithType:BRReturnToTheOriginalConditionTypeDefault];
                    }
                    
                }];
            }
            else {
                weakSelf.isPregnantSet = YES;
            }
        };
        
        if (self.takerInfoModel) {
            _writePatientInfoView.nickname = self.takerInfoModel.name;
            _writePatientInfoView.ageString = self.takerInfoModel.age;
            _writePatientInfoView.gender = [self.takerInfoModel.sex isEqualToString:@"1"] ? IMContactGenderMale : IMContactGenderFemale;
            _writePatientInfoView.isPregnant = self.takerInfoModel.isPregnant;
        }
    }
    //正常开方
    else{
        _patientView = [[BRPrescriptionPatientView alloc]init];
        [self.containerBgView addSubview:_patientView];
        
        [_patientView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.and.left.equalTo(weakSelf.containerBgView);
            make.width.mas_equalTo(SCREEN_WIDTH);
            make.height.mas_equalTo(105./2);
            
        }];
        
        _patientView.changeBlock = ^ {
            [weakSelf.view endEditing:YES];
            //先获取最新的患者信息
            [weakSelf getUserInfoWithType:BRGetPatientInfoTypeNotFirst];
        };
        
    }
    
#pragma mark 辨证
    
    _diagnosesView = [[BRPrescriptionDiagnosesView alloc]init];
    _diagnosesView.delegate = self;
    [self.containerBgView addSubview:_diagnosesView];
    
    [_diagnosesView mas_makeConstraints:^(MASConstraintMaker *make) {
       
        make.left.equalTo(weakSelf.view);
        //快速开方
        if (self.prescribeType == BRPrescribeTypeQuick || self.prescribeType == BRPrescribeTypeMessage) {
            make.top.equalTo(weakSelf.writePatientInfoView.mas_bottom).offset(15);
        }
        //正常开方
        else{
            make.top.equalTo(weakSelf.patientView.mas_bottom).offset(15);
        }
        make.width.mas_equalTo(SCREEN_WIDTH);
        make.height.mas_equalTo(kTitleViewHeight+75);
        
    }];
    
#pragma mark 用药
    
    _displayView = [[BRPrescriptionDisplayView alloc]init];
    [self.containerBgView addSubview:_displayView];
    
    [_displayView mas_makeConstraints:^(MASConstraintMaker *make) {
       
        make.top.equalTo(weakSelf.diagnosesView.mas_bottom).offset(VIEW_VERTICAL_MARGIN);
        make.height.mas_equalTo(kTitleViewHeight+75);
        make.width.mas_equalTo(SCREEN_WIDTH);
        make.left.equalTo(weakSelf.containerBgView);
        
    }];
    
    _displayView.addPrescriptionBlock = ^{
        //如果没有获取患者信息不让进入到添加药材页面
        //如果为快速开方，检查是否填写了用户信息
        if (weakSelf.prescribeType == BRPrescribeTypeNormal) {
            if (weakSelf.patientArray.count == 0) {
                [weakSelf.view makeToast:@"请选择患者" duration:kToastDuration position:CSToastPositionCenter];
                //[weakSelf getUserInfoWithType:BRGetPatientInfoTypeFirst];
                return;
            }
        }
        
        if (weakSelf.factoryArr.count > 0 && _drugArray.count == 0) {
            [weakSelf showSelectTypeActionSheetViewWithFactoryArr:weakSelf.factoryArr];
        }
        else {
            [weakSelf getFactoryInfoWithType:@"pressAddPrescription"];
        }
        
    };
    
    _displayView.updateHeight = ^(CGFloat height) {
        
        [weakSelf.view layoutIfNeeded];
        CGFloat oldDisplayViewHeight = weakSelf.displayView.height;
      
        [weakSelf.displayView mas_updateConstraints:^(MASConstraintMaker *make) {
            
            make.height.mas_equalTo(height);
            
        }];
        
    };
    
    _displayView.editButtonBlock = ^() {
        
        //跳转添加药材页面，不是编辑临时药方类型
        [weakSelf editSelectedDrugsWithType:NO];
        
    };
    
#pragma mark 用法用量
    _usageView = [[BRPresUsageView alloc]init];
    _usageView.patientId = self.patientId;
    
    //如果为快速开方 设置快速开方临时id
    if(self.prescribeType == BRPrescribeTypeQuick || self.prescribeType == BRPrescribeTypeMessage){
        _usageView.quickOrderTempId = self.quickOrderTempId;
    }else {
        _usageView.quickOrderTempId = @"";
    }
    [self.containerBgView addSubview:_usageView];
    
    [_usageView mas_makeConstraints:^(MASConstraintMaker *make) {
       
        make.top.equalTo(weakSelf.displayView.mas_bottom).offset(VIEW_VERTICAL_MARGIN);
        make.height.mas_equalTo(kTitleViewHeight+50+50);
        make.width.mas_equalTo(SCREEN_WIDTH);
        make.left.equalTo(weakSelf.containerBgView);
        
    }];
    
    //更改付数
    _usageView.drugChanged = ^ (NSString *drugNum) {
        if (![weakSelf.drugPrice isEqualToString:@"0"]) {
            [weakSelf calculateDrugCostsWithServiceProportion:weakSelf.serviceProportion type:BRCalculateChargeModifyALL];
        }
    };
    
    //选择剂量
    __weak BRPresUsageView *weakUsageView = _usageView;
    _usageView.textFieldEditingDidBegin = ^{
        UIView *inputView = [weakSelf showSelectDoseActionSheetView];
        weakUsageView.preDoseTextField.inputView = inputView;
    };
    
    // 胶囊计量选择事件已移除
    
    // 添加代煎剂型下"每剂分几次服用"选择事件
    _usageView.servingTimesSelectionBlock = ^{
        // 确保当前选择的是代煎剂型才启用此功能
        BRSubFactoryModel *currentSubFactory = [weakSelf getCurrentSubFactoryModel];
        BRFactoryModel *currentFactory = [weakSelf getCurrentFactoryModel];
        
        NSLog(@"servingTimesSelectionBlock 被调用，当前剂型: %@", currentFactory.drugFormName);
        
        if ([currentFactory.drugFormName isEqualToString:@"代煎"]) {
            NSLog(@"当前是代煎剂型，显示次数选择器");
            [weakSelf showServingTimesActionSheet];
            
            // 阻止键盘弹出
            [weakUsageView.timesTextField resignFirstResponder];
        } else {
            NSLog(@"当前不是代煎剂型，不显示次数选择器");
        }
    };
    
    // 添加规格选择事件
    _usageView.specificationSelectionBlock = ^{
        [weakSelf showSpecificationActionSheet];
    };
    
    // 添加辅料选择事件
    _usageView.auxiliaryMaterialSelectionBlock = ^{
        [weakSelf showAuxiliaryMaterialActionSheet];
    };
    
    //更新预计服用多长时间
    _usageView.changeDay = ^(NSString *dayStr) {
      
        BRFactoryModel *factoryModel = [weakSelf.factoryArr objectAtIndex:weakSelf.masterSelectedIndex];
        
        if (![factoryModel.drugFormName isEqualToString:@"颗粒"] &&
            ![factoryModel.drugFormName isEqualToString:@"饮片"] &&
            ![factoryModel.drugFormName isEqualToString:@"代煎"] &&
            ![factoryModel.drugFormName isEqualToString:@"外用中药"]) {
            weakSelf.usageView.dayTextField.text = dayStr;
            weakSelf.otherView.dayTextField.text = [NSString stringWithFormat:@"%d",[weakSelf.makeDays intValue] + [dayStr intValue]];
        }else {
            weakSelf.otherView.dayTextField.text = dayStr;
            weakSelf.otherView.dayTextField.text = dayStr;
        }
    };
    
    //更新用药时间
    _usageView.changeTime = ^ {
        
        [weakSelf.view endEditing:YES];
        
        if (_medicationTimeArr.count != 0) {
            [weakSelf showSelectMedicationTimeViewWithMedicationArr:weakSelf.medicationTimeArr];
            
            return ;
        }
        
        [weakSelf getMedicationTimeWithType:@"showMedicationTimeList"];
        
    };
    
    _usageView.changeUsageHeight = ^(CGFloat height) {
      
        [weakSelf.view layoutIfNeeded];
        CGFloat oldUsageViewHeight = weakUsageView.height;
        
        [weakUsageView mas_updateConstraints:^(MASConstraintMaker *make) {
           
            make.height.mas_equalTo(height);
            
        }];
        
//        weakSelf.scrollView.contentSize = CGSizeMake(kScreenWidth, weakSelf.scrollView.contentSize.height-oldUsageViewHeight+height);
        
    };
    
    _usageView.clickOtherTypeBlock = ^{
        [Utils br_showEstimateNumberOfDayWithBlock:^{
            
        }];
    };
    
#pragma mark 其它
    
    _otherView = [[BRPresOtherView alloc]init];
    _otherView.delegate = self;
    [self.containerBgView addSubview:_otherView];
    
    //显示代煎的默认文本
    // 如果当前是代煎剂型且有厂商数据，尝试初始化代煎偏好
    if (_factoryArr.count > 0 && _masterSelectedIndex < _factoryArr.count) {
        BRFactoryModel *currentFactory = [_factoryArr objectAtIndex:_masterSelectedIndex];
        if ([currentFactory.drugFormName isEqualToString:@"代煎"]) {
            [self initDecoctionPreferenceData];
            NSLog(@"configUI中初始化代煎偏好: %@", self.selectedDecoctionPreference);
        }
    }
    
    if (self.selectedDecoctionPreference.length > 0) {
        _otherView.decoctionPreference = self.selectedDecoctionPreference;
    } else {
        _otherView.decoctionPreference = @""; // 如果没有数据，显示空字符串
    }
    
    [_otherView mas_makeConstraints:^(MASConstraintMaker *make) {
        
        make.top.equalTo(weakSelf.usageView.mas_bottom).offset(VIEW_VERTICAL_MARGIN);
        make.height.mas_greaterThanOrEqualTo(kTitleViewHeight+50+50+50+50+50);
        make.width.mas_equalTo(SCREEN_WIDTH);
        make.left.equalTo(weakSelf.containerBgView);
        
    }];
    
    //代煎偏好选择
    _otherView.pressToselectDecoctionPreferenceBlock = ^{
        NSLog(@"选择代煎偏好= ===");
        [weakSelf showDecoctionPreferenceActionSheet];
    };
    
    //选择服药禁忌
    _otherView.selectContraindication = ^{
        
        [weakSelf.view endEditing:YES];
        
        if (_contraindicationArr.count != 0) {
            
            [weakSelf showContraindicationActionSheetViewWithContraindicationArr:weakSelf.contraindicationArr];
            
            return;
            
        }
        
        __block MBProgressHUD *hud = [Utils createLoadingHUD];
        //获取服药禁忌
        NSDictionary *dict = @{@"method_code":@"000252"};
        
        [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
            
        } success:^(NSURLSessionDataTask *task, id responseObject) {
            
            [hud hideAnimated:NO];
            
            NSString *code = [responseObject objectForKey:@"code"];
            if ([code isEqualToString:@"0000"]) {
                
                NSString *contraindicationStr = [responseObject objectForKey:@"data"];
                NSArray *conArr = [NSMutableArray arrayWithArray:[contraindicationStr componentsSeparatedByString:@";"]];
                
                //处理服药禁忌
                NSString *prescriptionStr = weakSelf.temPresModel.prescriptionStr;
                NSDictionary *presDict = [prescriptionStr mj_JSONObject];
                NSArray *contraindicationArr = [[presDict objectForKey:@"contraindication"] componentsSeparatedByString:@","];
                
                for (NSString *conStr in conArr) {
                    
                    BRContraindicationModel *model = [[BRContraindicationModel alloc]init];
                    model.contraindicationStr = conStr;
                    model.isSelected = NO;
                    
                    if ([contraindicationArr containsObject:conStr]) {
                        model.isSelected = YES;
                    }
                    
                    [weakSelf.contraindicationArr addObject:model];
                    
                }
                
                //显示服药禁忌
                [weakSelf showContraindicationActionSheetViewWithContraindicationArr:weakSelf.contraindicationArr];
                
            }
            else {
                
                NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
                [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
                
            }
            
            
        } failure:^(NSURLSessionDataTask *task, NSError *error) {
            
            [hud hideAnimated:NO];
            [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
            
        }];
    
        
    };
    
#pragma mark 按语
    
    _noteView = [[BRPresNoteView alloc]init];
    _noteView.delegate = self;
    [self.scrollView addSubview:_noteView];
    
    [_noteView mas_makeConstraints:^(MASConstraintMaker *make) {
       
        make.top.equalTo(weakSelf.otherView.mas_bottom).offset(VIEW_VERTICAL_MARGIN);
        make.height.mas_equalTo(kTitleViewHeight+150./2);
        make.width.mas_equalTo(SCREEN_WIDTH);
        make.left.equalTo(weakSelf.scrollView);
        
    }];
    
#pragma mark 订单金额
    
    _chargeView = [[BRPrescriptionChargeView alloc]init];
    [self.containerBgView addSubview:_chargeView];
    
    [_chargeView mas_makeConstraints:^(MASConstraintMaker *make) {
       
        make.top.equalTo(weakSelf.noteView.mas_bottom).offset(VIEW_VERTICAL_MARGIN);
        make.height.mas_equalTo(kTitleViewHeight+50+50+50);
        make.width.mas_equalTo(SCREEN_WIDTH);
        make.left.equalTo(weakSelf.containerBgView);
        
    }];
    
    //药价显示细节
    __weak BRPrescriptionChargeView *weakChargeView = _chargeView;
    _chargeView.pressToShowDetailBlock = ^(BOOL isShowDetail) {
        
        if (weakSelf.factoryArr.count == 0) {
            [weakSelf getFactoryInfoWithType:@"viewWillAppear"];
            return ;
        }
        
        weakSelf.isShowDetail = isShowDetail;
      
        [weakSelf.view layoutIfNeeded];
        
        CGFloat newChargeHeight = 0.0f;
        
        BRFactoryModel *factoryModel = [weakSelf.factoryArr objectAtIndex:weakSelf.masterSelectedIndex];
        // 修改判断条件：将颗粒和外用中药加入到代煎的判断逻辑中
        // 当药品形式不是饮片，或者是代煎/颗粒/外用中药且使用药房配置时，使用第一种高度计算方式
        if (![factoryModel.drugFormName isEqualToString:@"饮片"] ||
            (([factoryModel.drugFormName isEqualToString:@"代煎"] ||
              [factoryModel.drugFormName isEqualToString:@"颗粒"] ||
              [factoryModel.drugFormName isEqualToString:@"外用中药"]) && self.isUsePharmacyMake)) {
            
            if (isShowDetail) {
                
                if ([[[UserManager shareInstance] getApp_ShowMedicalServiceFeeRule] isEqualToString:@"0"]) {
                    newChargeHeight = kTitleViewHeight+50+(250-70)/2+50+50;
                }
                else {
                    newChargeHeight = kTitleViewHeight+50+250/2+50+50;
                }
                
            }
            else {
                
                newChargeHeight = kTitleViewHeight+50+50+50;
                
            }
            
        }
        else {
            
            if (isShowDetail) {
                
                if ([[[UserManager shareInstance] getApp_ShowMedicalServiceFeeRule] isEqualToString:@"0"]) {
                    newChargeHeight = kTitleViewHeight+50+(180-70)/2+50+50;
                }
                else {
                    newChargeHeight = kTitleViewHeight+50+180/2+50+50;
                }
                
            }
            else {
                
                newChargeHeight = kTitleViewHeight+50+50+50;
                
            }
            
        }
        
        [UIView animateWithDuration:.5 delay:0 usingSpringWithDamping:1 initialSpringVelocity:0.5 options:(UIViewAnimationCurveEaseInOut|UIViewAnimationOptionBeginFromCurrentState) animations:^{
            
            CGFloat chargeDiff = newChargeHeight -weakChargeView.height;
            [weakChargeView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(newChargeHeight);
            }];
            
            CGFloat oldContentHeight = weakSelf.scrollView.contentSize.height;
            
//            weakSelf.scrollView.contentSize = CGSizeMake(kScreenWidth, oldContentHeight+chargeDiff);
            
            [weakSelf.view layoutIfNeeded];
            
        } completion:^(BOOL finished) {
            
        }];
        
    };
    
    //改变诊费中
    _chargeView.chargeTextFieldEditingChanged = ^(NSString *text) {
        //单纯计算加上诊费后的金额
        NSDecimalNumberHandler*roundingBehavior = [NSDecimalNumberHandler decimalNumberHandlerWithRoundingMode:NSRoundUp scale:2 raiseOnExactness:NO raiseOnOverflow:NO raiseOnUnderflow:NO raiseOnDivideByZero:YES];
        
        NSDecimalNumber *chargeDecimal = [NSDecimalNumber decimalNumberWithString:weakSelf.changedCharge];
        chargeDecimal = [chargeDecimal decimalNumberByMultiplyingBy:[NSDecimalNumber decimalNumberWithString:@"1.064"]];
        
        weakSelf.totalPriceDecimal = [weakSelf.totalPriceDecimal decimalNumberBySubtracting:chargeDecimal];
        
        // 验证输入文本，防止无效字符串导致异常
        NSString *validText = text && text.length > 0 ? text : @"0";
        NSDecimalNumber *newChargeDecimal = [NSDecimalNumber decimalNumberWithString:validText];
        newChargeDecimal = [newChargeDecimal decimalNumberByMultiplyingBy:[NSDecimalNumber decimalNumberWithString:@"1.064"]];
        weakSelf.totalPriceDecimal = [weakSelf.totalPriceDecimal decimalNumberByAdding:newChargeDecimal];
        
        [weakSelf setTotalPriceWithTotalPriceDecimal:[weakSelf.totalPriceDecimal decimalNumberByRoundingAccordingToBehavior:roundingBehavior]];
        
        weakSelf.changedCharge = text;
    };
    
    //改变诊费后
    _chargeView.textFieldEditingChanged = ^(NSString *text) {
        
        //如果药价为空那么不用算
        if (weakSelf.drugPrice.length == 0 || [weakSelf.drugPrice floatValue] == 0) {
            return ;
        }
        
        /*
        //把新的医技服务费传过去
        [weakSelf calculateDrugCostsWithServiceProportion:weakSelf.serviceProportion type:BRCalculateChargeOnlyModifyDrugCosts];
         */
        
        NSNumberFormatter *numberFormatter = [[NSNumberFormatter alloc]init];
        [numberFormatter setPositiveFormat:@"0.00"];
        
        BRFactoryModel *factoryModel = [weakSelf.factoryArr objectAtIndex:weakSelf.masterSelectedIndex];
        NSArray *factoryArr = factoryModel.list;
        BRSubFactoryModel *subFactoryModel = [factoryArr objectAtIndex:weakSelf.detailSelectedIndex];
        
        //计算基础药费
        NSDecimalNumber *drugPriceDecimal = [NSDecimalNumber decimalNumberWithString:weakSelf.drugPrice];
        
        if ([factoryModel.drugFormName isEqualToString:@"颗粒"] ||
            [factoryModel.drugFormName isEqualToString:@"饮片"] ||
            [factoryModel.drugFormName isEqualToString:@"代煎"] ||
            [factoryModel.drugFormName isEqualToString:@"外用中药"]) {
            
            // 验证剂数输入，防止无效字符串导致异常
            NSString *drugNumText = weakSelf.usageView.drugNumTextField.text;
            NSString *validDrugNumText = (drugNumText && drugNumText.length > 0) ? drugNumText : @"1";
            NSDecimalNumber *drugNumDecimal = [NSDecimalNumber decimalNumberWithString:validDrugNumText];
            //总基础药价（不带算式）
            NSDecimalNumber *totalDrugPriceDecimal = [drugPriceDecimal decimalNumberByMultiplyingBy:drugNumDecimal];
            drugPriceDecimal = [NSDecimalNumber decimalNumberWithString:[NSString stringWithFormat:@"%@",totalDrugPriceDecimal]];
            
        }
        
        //计算出比例
        NSDecimalNumberHandler*roundingDownBehavior = [NSDecimalNumberHandler decimalNumberHandlerWithRoundingMode:NSRoundDown scale:2 raiseOnExactness:NO raiseOnOverflow:NO raiseOnUnderflow:NO raiseOnDivideByZero:YES];
        
        // 验证输入文本，防止无效字符串导致异常
        NSString *validText = text && text.length > 0 ? text : @"0";
        NSDecimalNumber *serviceChargeDecimal = [NSDecimalNumber decimalNumberWithString:validText];
        NSDecimalNumber *denominator = [drugPriceDecimal decimalNumberByAdding:serviceChargeDecimal];
        NSDecimalNumber *proportion;
        
        // 防止除零和过大比例值
        if ([denominator compare:[NSDecimalNumber decimalNumberWithString:@"0.01"]] == NSOrderedAscending) {
            // 分母过小时，设置一个合理的比例上限
            proportion = [NSDecimalNumber decimalNumberWithString:@"0.99"];
        } else {
            proportion = [serviceChargeDecimal decimalNumberByDividingBy:denominator];
            // 限制比例不超过0.99，防止后续计算中1-proportion接近0
            if ([proportion compare:[NSDecimalNumber decimalNumberWithString:@"0.99"]] == NSOrderedDescending) {
                proportion = [NSDecimalNumber decimalNumberWithString:@"0.99"];
            }
        }
        
        weakSelf.serviceProportion = [NSString stringWithFormat:@"%@",proportion];
        
        [weakSelf calculateDrugCostsWithServiceProportion:[NSString stringWithFormat:@"%@",proportion] type:BRCalculateChargeOnlyModifyDrugCosts];
        
    };
    
    _chargeView.clickBaseFeeInfoBlock = ^{
        [Utils br_showBaseFeeTipsWithBlock:^{
            
        }];
    };
    
    
    _chargeView.clickAppendFeeInfoBlock = ^{
        [Utils br_showAdditoinalFeeTipsWithBlock:^{
            
        }];
    };
    
    _chargeView.clickAllBtnInfoBlock = ^{
        [Utils br_showSumCountFeeTipsWithBlock:^{
            
        }];
    };
    
#pragma mark 同意线下诊断
    
    _offlineView = [[BRPresOfflineView alloc]init];
    [self.containerBgView addSubview:_offlineView];
    
    [_offlineView mas_makeConstraints:^(MASConstraintMaker *make) {
       
        make.top.equalTo(weakSelf.chargeView.mas_bottom).offset(VIEW_VERTICAL_MARGIN);
        if (isiPhone5) {
            make.height.mas_equalTo(208./2);
        }
        else {
            make.height.mas_equalTo(168./2);
        }
        make.width.mas_equalTo(SCREEN_WIDTH);
        make.left.equalTo(weakSelf.containerBgView);
        
        make.bottom.mas_lessThanOrEqualTo(@-20);
        
    }];
    
//    _scrollView.contentSize = CGSizeMake(SCREEN_WIDTH, 105./2 + (kTitleViewHeight+75) + (kTitleViewHeight+75) + (kTitleViewHeight+50+50) + (kTitleViewHeight+50+50+50+50) + (kTitleViewHeight+150./2) + (kTitleViewHeight+50+50+50) + (isiPhone5?(208./2):(168./2)) + 15 + (10*6) + 25 + kTabbarSafeBottomMargin + 50 + 50 + 25);
    
    _createButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_createButton setTitle:@"生成医案" forState:UIControlStateNormal];
    [_createButton setBackgroundColor:[UIColor br_mainBlueColor]];
    [_createButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [_createButton addTarget:self action:@selector(pressToCreate) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:_createButton];
    
    [_createButton mas_makeConstraints:^(MASConstraintMaker *make) {
       
        make.left.and.right.equalTo(weakSelf.view);
        make.bottom.equalTo(weakSelf.view).with.offset(-kTabbarSafeBottomMargin);
        make.height.mas_equalTo(45);
        
    }];
    
}
#pragma mark 患者信息赋值
- (void)setPatientInfoWithPatientIndex:(NSInteger)index {
    
    BRPatientModel *model = [_patientArray objectAtIndex:index];
    
    [self setPatientInfoWithPatientModel:model isSync:YES];
    
}

- (void)setPatientInfoWithPatientModel:(BRPatientModel *)model isSync:(BOOL)isSync{
    
    if (isSync) {
        //发送通知，让医案界面选择的用户信息同步
        NSDictionary *userInfo = @{
            @"id" : model.patientId,
            @"name" : model.name
        };
        
        [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationPrescriptionChangePatient object:nil userInfo:userInfo];
    }
     
    //
    for (int i = 0; i < self.patientArray.count; i++) {
        BRPatientModel *patientModel = [self.patientArray objectAtIndex:i];
        if ([patientModel.patientId isEqualToString:model.patientId]) {
            model = patientModel;
            break;
        }
    }
    
    if ([model.isSelf isEqualToString:@"1"]) {
        
        NSString *subNameStr = model.name;
        if (model.name.length > 4) {
            NSString *name = [model.name substringToIndex:4];
            subNameStr = [NSString stringWithFormat:@"%@...",name];
        }
        
        NSMutableAttributedString *
        nameStr = [[NSMutableAttributedString alloc]initWithString:[NSString stringWithFormat:@"%@(本人)",subNameStr]];
        [nameStr addAttribute:NSFontAttributeName value:kFontLight(12) range:NSMakeRange(nameStr.length-4, 4)];
        [nameStr addAttribute:NSForegroundColorAttributeName value:[UIColor colorWithHex:0x919ba7] range:NSMakeRange(nameStr.length-4, 4)];
        
        [_patientView setPatientNameAttributedString:nameStr];
        
    }
    else {
        NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:model.name];
        
        [_patientView setPatientNameAttributedString:attributedString];
    }
    
    if ([model.sex isEqualToString:@"1"]) {
        
        _patientView.gender = IMContactGenderMale;
        
    }
    else {
        
        _patientView.gender = IMContactGenderFemale;
        
    }
    
    _patientView.ageString = model.age;
    
    if ((_patientView.gender == IMContactGenderMale) || ([model.isPregnant isEqualToString:@"0"] || [model.isPregnant isEqualToString:@""])) {
        //未怀孕
        _patientView.pregnantLabel.hidden = YES;
    }
    else {
        //怀孕
        _patientView.pregnantLabel.hidden = NO;
        
    }
    
    _patientModel = model;
    
}
#pragma mark - 与医案界面同步更新选择的患者
- (void)syncPatientDocumentWithNotification:(NSNotification *)notification {
    
    NSDictionary *userInfo = [notification userInfo];
    BRPatientModel *model = [[BRPatientModel alloc] init];
    model.isSelf = [userInfo objectForKey:@"isSelf"];
    model.age = [userInfo objectForKey:@"age"];
    model.patientId = [userInfo objectForKey:@"id"];
    model.isPregnant = [userInfo objectForKey:@"isPregnant"];
    model.name = [userInfo objectForKey:@"name"];
    model.sex = [userInfo objectForKey:@"sex"];
    model.birthday = [userInfo objectForKey:@"birthday"];

    if (![self.patientModel.isPregnant isEqualToString:model.isPregnant] && self.drugArray.count > 0) {
        //切换怀孕信息，用药信息将清空
        [self returnToTheOriginalConditionWithType:BRReturnToTheOriginalConditionTypeDefault];
    }
    
    [self setPatientInfoWithPatientModel:model isSync:NO];
}





#pragma mark 弹出选择患者界面
- (void)showSelectPatientsViewWithPatientsArr:(NSArray *)patientsArr {
    
    NSMutableArray *patientNameArr = [[NSMutableArray alloc]init];
    for (BRPatientModel *model in patientsArr) {
        [patientNameArr addObject:model.name];
    }
    
     __weak PrescriptionViewController *weakSelf = self;
    
    BRActionSheetView *actionSheetView = [[BRActionSheetView alloc]init];
    actionSheetView.title = @"选择开方患者";
    actionSheetView.bottomTitle = @"新增患者";
    actionSheetView.bottomIcon = [UIImage imageNamed:@"prescription_add_patient"];
    [actionSheetView setButtons:patientNameArr withSelfIndex:1 withWithdrawMethod:NO];
    actionSheetView.topButtonTitle = @"删除";
    actionSheetView.topButtonColor = [UIColor br_textRedColor];
    actionSheetView.isBtnDeleteAble = NO;
    [actionSheetView show];
    
    __weak BRActionSheetView *weakActionSheet = actionSheetView;
    actionSheetView.clickBottomButtonCallBack = ^{
        
        //删除状态
        if (weakActionSheet.isBtnDeleteAble) {
            if (weakActionSheet.toDeleteArray.count == 0) {
                [weakActionSheet makeToast:@"您没有选择患者，不能删除" duration:2 position:CSToastPositionCenter];
                return;
            }

            //确认删除患者
            
            BRAlertView *alertView = [[BRAlertView alloc] init];
            [alertView.okButton setTitle:@"确 认" forState:UIControlStateNormal];
            [alertView.cancelButton setTitle:@"取 消" forState:UIControlStateNormal];
            
            [alertView showAlertViewWithCancelButton:@"确认删选择的患者？" completion:^(BOOL isOk) {
                //操作删除
                if (isOk) {
                    //删除指定患者
                    [weakSelf deletePatientFamilyMemberWithArray:weakActionSheet.toDeleteArray];
                    
                    [weakActionSheet close];
                }
                
                
                [alertView close];
            }];
        }
        else {
            
            [weakActionSheet close];
            //增加患者
            BRPresAddPatientView *patientView = [[BRPresAddPatientView alloc]init];
            patientView.patientId = weakSelf.patientId;
            [patientView showAddPatientView];
            
            __weak BRPresAddPatientView *weakAddPatientView = patientView;
            patientView.addPatientSuccess = ^ (BRPatientModel *patientModel){
                [weakAddPatientView close];
                
                [weakSelf updatePatientInfoList:patientModel];
            };
        }
    };
    
    actionSheetView.clickActionButtonCallBack = ^(NSInteger index) {
        
        [weakActionSheet close];
        
        [weakSelf choosePatientsWithIndex:index];
    };
    
    //删除
    actionSheetView.clickRightCallBack = ^{
        NSLog(@"删除====");
        
        weakActionSheet.isBtnDeleteAble = !weakActionSheet.isBtnDeleteAble;
        
//        [weakSelf updatePatientInfoList];
    };
    
    //编辑
    actionSheetView.clickLongPressButtonCallBack = ^(NSInteger index) {
        [weakActionSheet close];
        
        //开始编辑
        [weakSelf editFamilyMemberWithIndex:index];
    };
    
    self.actionSheetView = actionSheetView;
    [self.actionSheetView show];
    
}

//增加删除后更新患者信息
- (void)updatePatientInfoList:(BRPatientModel *)model {
    
        //获取所有患者信息
        NSDictionary *dict = @{@"method_code":@"000232",
                               @"patientId":self.patientId
                               };
        
        __weak PrescriptionViewController *weakSelf = self;
        
        MBProgressHUD *hud = [Utils createLoadingHUDWithTitle:@""];
        
        [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
            
        } success:^(NSURLSessionDataTask *task, id responseObject) {
            
            hud.hidden = YES;
            
            NSString *code = [responseObject objectForKey:@"code"];
            if ([code isEqualToString:@"0000"]) {
                
                weakSelf.patientArray = [BRPatientModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"patients"]];
                
                if (weakSelf.patientArray.count<2) {
                    return ;
                }
                
                BRPatientModel *patientModel = [weakSelf.patientArray objectAtIndex:1];
                
                if (![patientModel.isPregnant isEqualToString:weakSelf.patientModel.isPregnant] && weakSelf.drugArray.count > 0) {
                    BRAlertView *alertView = [[BRAlertView alloc]init];
                    alertView.isHideWhenTapBackground = YES;
                    [alertView showAlertViewWithCancelButton:@"切换怀孕状态，已填用药信息将清空。" completion:^(BOOL isOk) {
                        
                        if (!isOk) {
                            return ;
                        }
                        else {
                            
                            [weakSelf returnToTheOriginalConditionWithType:BRReturnToTheOriginalConditionTypeDefault];
//                            [weakSelf setPatientInfoWithPatientIndex:index];
                            if (model == nil) {
                                BRPatientModel *curModel = [weakSelf.patientArray objectAtIndex:0];
                                [weakSelf setPatientInfoWithPatientModel:curModel isSync:YES];
                            }
                            else {
                                
                                [weakSelf setPatientInfoWithPatientModel:model isSync:YES];
                            }
                            
                            
                        }
                        
                    }];
                    
                }
                else {
//                    [weakSelf setPatientInfoWithPatientIndex:1];
                    if (model == nil) {
                        BRPatientModel *curModel = [weakSelf.patientArray objectAtIndex:0];
                        [weakSelf setPatientInfoWithPatientModel:curModel isSync:YES];
                    }
                    else {
                        [weakSelf setPatientInfoWithPatientModel:model isSync:YES];
                    }
                }
                
            }
            else {
                
                NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
                [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
                
            }
            
            
        } failure:^(NSURLSessionDataTask *task, NSError *error) {
            
            if (hud) {
                hud.hidden = YES;
            }
            
            [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
            
        }];
        
    
}

//删除患者家属
- (void)deletePatientFamilyMemberWithArray:(NSArray *)toDeleteArray {
    NSLog(@"delete array == %@",toDeleteArray);
    
    NSMutableArray *toDeleteIdArray = [[NSMutableArray alloc] initWithCapacity:0];
    
    BOOL containFlag = NO;
    
    for (int i = 0; i < self.patientArray.count; i++) {
        //删除
        if ([toDeleteArray containsObject:@(i)]) {
            BRPatientModel *model = [self.patientArray objectAtIndex:i];
            [toDeleteIdArray addObject:model.patientId];
            
            if ([model.patientId isEqualToString:self.patientModel.patientId]) {
                containFlag = YES;
            }
        }
    }
    
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithDictionary:@{
        @"method_code" : @"000441",
        @"patientId" : self.patientId,
//        @"takerIdList" : [toDeleteIdArray mj_JSONString]
    }];
    
    if (toDeleteIdArray.count > 0) {
        [dict setValue:[toDeleteIdArray mj_JSONString] forKey:@"takerIdList"];
    }
    
    __weak __typeof(self)weakSelf = self;
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            //删除成功
            [weakSelf.view makeToast:@"删除成功" duration:2 position:CSToastPositionCenter];
            
            //更新 当前被删除
            if (containFlag) {
                [weakSelf updatePatientInfoList:nil];
            }
        }
        else {
            NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
            [weakSelf.view makeToast:errorMsg duration:2 position:CSToastPositionCenter];
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [weakSelf.view makeToast:error.localizedDescription duration:2 position:CSToastPositionCenter];
    }];
}

//编辑患者家属
- (void)editFamilyMemberWithIndex:(NSInteger)editIndex {
    
    if (editIndex >= self.patientArray.count) {
        return;
    }
    
    BRPatientModel *editModel = [self.patientArray objectAtIndex:editIndex];
    
    if ([editModel.isSelf isEqualToString:@"1"]) {
        
        BRAlertView *alertView = [[BRAlertView alloc] init];
        alertView.isHideWhenTapBackground = YES;
        
        [alertView showAlertView:@"患者家属信息医生可修改，患者本人信息需要医生告知患者在必然甘草公众号中修改" completion:^{
            [alertView close];
            //
        }];
        
        return;
    }
    
    BRPresAddPatientView *editPatientView = [[BRPresAddPatientView alloc] init];
    editPatientView.patientId = self.patientId;
    editPatientView.updateModel = editModel;
    [editPatientView showAddPatientView];
    
    __weak BRPresAddPatientView *weakEditPatientView = editPatientView;
    __weak __typeof(self)weakSelf = self;
    
    editPatientView.editPatientSuccess = ^(NSString *userId) {
        [weakEditPatientView close];
        BRPatientModel *patientModel = [weakSelf.patientArray objectAtIndex:0];

        for (int i = 0; i < weakSelf.patientArray.count; i++) {
            BRPatientModel *model = [weakSelf.patientArray objectAtIndex:i];
            if ([model.patientId isEqualToString:userId]) {
                patientModel = model;
                break;
            }
        }
        
//        if ([editModel.isSelf isEqualToString:@"1"]) {
            [weakSelf updatePatientInfoList:patientModel];
            
            //更新标题
            if (weakSelf.updatePatientNameBlock) {
                weakSelf.updatePatientNameBlock();
            }
//        }
    };
}

- (void)choosePatientsWithIndex:(NSInteger)index {
    
    BRPatientModel *model = [self.patientArray objectAtIndex:index];
    
    if (model.isPregnant.length == 0) {
        model.isPregnant = @"0";
    }
    
    if (self.patientModel.isPregnant.length == 0) {
        self.patientModel.isPregnant = @"0";
    }
    
    if (![model.isPregnant isEqualToString:self.patientModel.isPregnant] && self.drugArray.count > 0) {
        
        BRAlertView *alertView = [[BRAlertView alloc]init];
        alertView.isHideWhenTapBackground = YES;
        [alertView showAlertViewWithCancelButton:@"切换怀孕状态，已填用药信息将清空。" completion:^(BOOL isOk) {
            
            if (!isOk) {
                return ;
            }
            else {
                
                [self returnToTheOriginalConditionWithType:BRReturnToTheOriginalConditionTypeDefault];
                [self setPatientInfoWithPatientIndex:index];
                
            }
            
        }];
    }
    else {
        [self setPatientInfoWithPatientIndex:index];
    }
}

#pragma mark 跳转 编辑药材
/**
 * isTempPres 是不是临时药方
 **/
- (void)editSelectedDrugsWithType:(BOOL)isTempPres {
    
    //判断是否获取到了患者的信息，如果没有获取到患者信息，先获取患者信息
    if (_patientArray.count == 0 && self.prescribeType == BRPrescribeTypeNormal) {
        [self.view makeToast:@"请选择患者" duration:kToastDuration position:CSToastPositionCenter];
        //[self getUserInfoWithType:BRGetPatientInfoTypeFirst];
        return ;
    }
    
    AddDrugViewController *addDrugVC = [[AddDrugViewController alloc]init];
    addDrugVC.patientId = _patientModel.patientId;
    addDrugVC.patientModel = _patientModel;
    addDrugVC.delegate = self;
    addDrugVC.isTempPres = isTempPres;
    addDrugVC.prescribeType = self.prescribeType;
    addDrugVC.takerIsPregnant = self.patient_is_pregnanet;
    [addDrugVC setTitleWithFactoryArray:_factoryArr masterSelectedIndex:_masterSelectedIndex detailSelectedIndex:_detailSelectedIndex];
    if (_drugArray.count > 0) {
        addDrugVC.drugArray = _drugArray;
        
        if (isTempPres) {
            //如果为临时处方要清除，如果返回不保存药材的话，药材还在
            [_drugArray removeAllObjects];
        }
        
    }
    [self.navigationController pushViewController:addDrugVC animated:YES];
    
}

#pragma mark 获取用药时间
- (void)getMedicationTimeWithType:(NSString *)type {
    
    //获取用药时间
    NSDictionary *dict = @{@"method_code":@"000234"};
    
    __weak PrescriptionViewController *weakSelf = self;
    
    MBProgressHUD *hud = [Utils createLoadingHUDWithTitle:@""];
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        [hud hideAnimated:YES];
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            
            if (![[responseObject objectForKey:@"medicationTime2"] isKindOfClass:[NSNull class]]) {
                weakSelf.medicationTimeArr = [NSMutableArray arrayWithArray:[responseObject objectForKey:@"medicationTime2"]];
            }
            [weakSelf.medicationTimeArr addObjectsFromArray:[responseObject objectForKey:@"medicationTime1"]];
            
            //需要显示用药时间
            if ([type isEqualToString:@"showMedicationTimeList"]) {
                [weakSelf showSelectMedicationTimeViewWithMedicationArr:weakSelf.medicationTimeArr];
            }
            
        }
        else {
            NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
            [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
        }
        
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [hud hideAnimated:YES];
        [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
    }];
    
}

#pragma mark - 弹出选择代煎偏好弹窗
- (void)showDecoctionPreferenceActionSheet {
    // 检查是否有代煎偏好数据
    if (!self.decoctionPreferenceArray || self.decoctionPreferenceArray.count == 0) {
        NSLog(@"没有代煎偏好数据可选择");
        return;
    }
    
    _decoctionPreferenceActionSheet = [[BRActionSheetView alloc] init];
    _decoctionPreferenceActionSheet.title = @"选择代煎偏好";
    _decoctionPreferenceActionSheet.buttons = self.decoctionPreferenceArray;
    [_decoctionPreferenceActionSheet show];
    
    __weak typeof(self) weakSelf = self;
    __weak BRActionSheetView *weakActionSheet = _decoctionPreferenceActionSheet;
    _decoctionPreferenceActionSheet.clickActionButtonCallBack = ^(NSInteger index) {
        [weakActionSheet close];
        
        // 更新选中的代煎偏好
        weakSelf.decoctionPreferenceIndex = index;
        NSString *selectedPreference = weakSelf.decoctionPreferenceArray[index];
        weakSelf.selectedDecoctionPreference = selectedPreference;
        
        // 更新显示
        weakSelf.otherView.decoctionPreference = selectedPreference;
        
        NSLog(@"选择的代煎偏好: %@，索引: %lu", selectedPreference, (unsigned long)index);
    };
}

#pragma mark 弹出选择用药时间界面
- (void)showSelectMedicationTimeViewWithMedicationArr:(NSArray *)medicationArr {
    
    _timeActionSheet = [[BRActionSheetView alloc]init];
    _timeActionSheet.title = @"选择用药时间";
    _timeActionSheet.bottomTitle = @"自定义";
    _timeActionSheet.buttons = medicationArr;
    [_timeActionSheet show];
    
    __weak PrescriptionViewController *weakSelf = self;
    __weak BRActionSheetView *weakActionSheet = _timeActionSheet;
    _timeActionSheet.clickActionButtonCallBack = ^(NSInteger index) {
        
        [weakActionSheet close];
        
        NSString *timeStr = [weakSelf.medicationTimeArr objectAtIndex:index];
        [weakSelf.usageView.changeTimeButton setTitle:timeStr forState:UIControlStateNormal];
        
    };
    
    _timeActionSheet.clickBottomButtonCallBack = ^{
        
        [weakSelf.timeActionSheet close];
        
        BRAlertView *alertView = [[BRAlertView alloc] init];
        alertView.isHideWhenTapBackground = YES;
        __weak BRAlertView *weakAlertView = alertView;
        [alertView showAlertViewWithInputView:@"请输入用药时间" placeholder:@"请输入用药时间" contentText:@"" completion:^(BOOL isOk, NSString * _Nullable text) {
            
            if (!isOk) {
                return ;
            }
            
            NSString *newText = [text stringByReplacingOccurrencesOfString:@" " withString:@""];
            NSString *regex = @"[0-9\u4e00-\u9fa5]+";
            NSPredicate *pre = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
            if (newText.length == 0) {
                
                [weakAlertView makeToast:@"请输入用药时间" duration:2 position:CSToastPositionCenter];
                return;
                
            }
            else if (![pre evaluateWithObject:newText]) {
                
                [weakAlertView makeToast:@"只支持输入汉字、数字" duration:2 position:CSToastPositionCenter];
                return;
                
            }
            else {
                
                NSDictionary *dict = @{@"method_code":@"000235",
                                       @"title":text
                                       };
                
                [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
                    
                } success:^(NSURLSessionDataTask *task, id responseObject) {
                    
                    NSString *code = [responseObject objectForKey:@"code"];
                    if ([code isEqualToString:@"0000"]) {
                        
                        //[weakSelf.medicationTimeArr insertObject:text atIndex:0];
                        [weakSelf getMedicationTimeWithType:@"updateMedicationTimeList"];
                        
                        [weakAlertView close];
                        
                        [weakSelf.usageView.changeTimeButton setTitle:newText forState:UIControlStateNormal];
                        
                    }
                    else {
                        
                        NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
                        [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
                        
                    }
                    
                    
                } failure:^(NSURLSessionDataTask *task, NSError *error) {
                    
                    [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
                    
                }];
                
            }
            
        }];
      
    };
    
}

#pragma mark 弹出选择服药禁忌
- (void)showContraindicationActionSheetViewWithContraindicationArr:(NSArray *)contraindicationArr {
    
    _contraindicationView = [[BRPresContraindicationView alloc]init];
    _contraindicationView.title = @"请选择服药禁忌";
    _contraindicationView.contraindicationArr =contraindicationArr;
    _contraindicationView.bottomTitle = @"确定";
    _contraindicationView.topButtonTitle = @"清空";
    [_contraindicationView show];
    
    __weak PrescriptionViewController *weakSelf = self;
    _contraindicationView.clickBottomButton = ^(NSMutableArray *contraindicationArr) {
      
        NSString *contraindicationStr = [contraindicationArr componentsJoinedByString:@","];
        [weakSelf.otherView setContraindicationStr:contraindicationStr];
        [weakSelf.contraindicationView close];
        
    };
    
}

#pragma mark 弹出选择剂量
- (UIView *)showSelectDoseActionSheetView {
    
    NSArray *buttonsArr = @[@"3g",@"6g",@"9g"];
    
    BRActionSheetView *actionSheet = [[BRActionSheetView alloc]init];
    actionSheet.title = @"选择用量";
    actionSheet.buttons = buttonsArr;
    [actionSheet show];
    
    __block NSString *preDoseStr = @"6";
    __weak PrescriptionViewController *weakSelf = self;
    __weak BRPresUsageView *weakUsageView = _usageView;
    __weak BRActionSheetView *weakActionSheet = actionSheet;
    __weak BRPresOtherView *weakOtherView = _otherView;
    actionSheet.clickActionButtonCallBack = ^(NSInteger index) {
        
        [weakActionSheet close];
        
        NSString *preDoseStr = [buttonsArr objectAtIndex:index];
        NSArray *preDoseArr = [preDoseStr componentsSeparatedByString:@"g"];
        
        preDoseStr = [preDoseArr firstObject];
        
        weakUsageView.preDoseTextField.text = preDoseStr;
        //计算预计服用天数
        NSString *timesNum = [weakUsageView.timesTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        NSString *preDoseNum = [weakUsageView.preDoseTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        
        //保存每次多少克 和 预计服用天数
        NSString *drugFormmd5 = [[weakSelf.currentDrugForm md5String] substringToIndex:5];
        
        NSString *kOtherTypeEveryTimeAmount = [NSString stringWithFormat:@"%@_%@",kUsageOtherType_EveryTimesAmount, drugFormmd5];
        
        NSString *kOtherTypeEstimateDays = [NSString stringWithFormat:@"%@_%@", kUsageOtherType_EstimateDays, drugFormmd5];
        
        [Utils saveUsageType:kOtherTypeEveryTimeAmount text:preDoseNum withPatientId:weakSelf.patientId];
        [Utils saveUsageType:kOtherTypeEstimateDays text:timesNum withPatientId:weakSelf.patientId];
        
        
        if ([timesNum intValue] == 0 || [preDoseNum integerValue] == 0 || timesNum.length == 0 || preDoseNum.length == 0) {
            
        }
        else {
            
            CGFloat totalDose = [_balanceWeight integerValue];
            NSInteger day = totalDose/([timesNum integerValue]*[preDoseNum integerValue]);
            weakUsageView.dayTextField.text = [NSString stringWithFormat:@"%ld",day == 0? 1:day];
            weakOtherView.dayTextField.text = [NSString stringWithFormat:@"%ld",[weakSelf.makeDays intValue] + (day == 0? 1:day)];
        }
            
        
    };
    
    actionSheet.actionSheetViewClosed = ^{
      
        [weakUsageView.preDoseTextField resignFirstResponder];
        
    };
    
    return actionSheet;
    
}

#pragma mark - 胶囊剂型弹出选择剂量
- (UIView *)showCapsuleSelectDoseActionSheetView {
    
    //获取当前的厂商
    BRSubFactoryModel *currentSub = [self getCurrentSubFactoryModel];
    
    
    //获取到每粒多少克  从接口已有数据中获取，或者
    CGFloat weightPerGrain = [currentSub.dosePreUnit floatValue];
    NSArray *weightArray = @[@(1.5), @(3.0), @(4.5)];
    NSMutableArray *buttonsArray = [NSMutableArray arrayWithCapacity:0];
    
    for (int i = 0; i < weightArray.count; i++) {
        //克数
        CGFloat weight = [[weightArray objectAtIndex:i] floatValue];
        //计算出是多少颗
        NSInteger pillCount = weight / weightPerGrain;
        
        //表述
        NSString *text = [NSString stringWithFormat:@"%.1fg  【%lu颗】",weight,pillCount];
        
        [buttonsArray addObject:text];
    }
   
    //组合 例如 ：每颗约0.5g
    NSString *titleAppendStr = [NSString stringWithFormat:@"(每%@约%@%@)",currentSub.packageUnit,currentSub.dosePreUnit,currentSub.packageDoseUnit];
    
    NSString *titleStr = [NSString stringWithFormat:@"用法用量%@",titleAppendStr];
    
    NSRange range = [titleStr rangeOfString:titleAppendStr];
    
    // 创建一个 NSMutableAttributedString
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:titleStr attributes:@{
        NSForegroundColorAttributeName : [UIColor br_textBlackColor],
        NSFontAttributeName : kFontLight(17)
    }];

    [attributedString addAttributes:@{
        NSForegroundColorAttributeName : [UIColor br_textRedColor],
        NSFontAttributeName : kFontLight(15)
    } range:range];
    
    
    
    BRActionSheetView *actionSheet = [[BRActionSheetView alloc] init];
    actionSheet.buttons = buttonsArray;
    actionSheet.bottomTitle = @"自定义";
    [actionSheet show];
    
    actionSheet.titleAttributedString = attributedString;
    
    __weak PrescriptionViewController *weakSelf = self;
    __weak BRPresUsageView *weakUsageView = _usageView;
    __weak BRActionSheetView *weakActionSheet = actionSheet;
    __weak BRPresOtherView *weakOtherView = _otherView;
    
    
    actionSheet.clickBottomButtonCallBack = ^{
        [weakActionSheet close];
        //自定义用法用量
        [weakSelf showCustomCapsuleWeightPopupWithCapsuleWight:[currentSub.dosePreUnit floatValue] capsuleWeightUnit:currentSub.packageDoseUnit];
    };
    
    
    actionSheet.clickActionButtonCallBack = ^(NSInteger index) {
        [weakActionSheet close];
        
        // 直接从 weightArray 获取选中的克数
        CGFloat selectedWeight = [[weightArray objectAtIndex:index] floatValue];
//        NSString *doseStr = [NSString stringWithFormat:@"%.1f", selectedWeight];
        
        // 更新剂量文本框
//        weakUsageView.preDoseTextField.text = doseStr;
        
        //更新预计服用天数
        [weakSelf updateEstimateTakeNumbersWithWeight:selectedWeight];
//        // 计算预计服用天数
//        NSString *timesNum = [weakUsageView.timesTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
//        
//        // 保存每次多少克和预计服用天数
//        NSString *drugFormmd5 = [[weakSelf.currentDrugForm md5String] substringToIndex:5];
//        NSString *kOtherTypeEveryTimeAmount = [NSString stringWithFormat:@"%@_%@", kUsageOtherType_EveryTimesAmount, drugFormmd5];
//        NSString *kOtherTypeEstimateDays = [NSString stringWithFormat:@"%@_%@", kUsageOtherType_EstimateDays, drugFormmd5];
//        
//        [Utils saveUsageType:kOtherTypeEveryTimeAmount text:doseStr withPatientId:weakSelf.patientId];
//        [Utils saveUsageType:kOtherTypeEstimateDays text:timesNum withPatientId:weakSelf.patientId];
//        
//        if ([timesNum intValue] == 0 || selectedWeight == 0 || timesNum.length == 0) {
//            // 输入无效，不进行计算
//        } else {
//            CGFloat totalDose = [weakSelf.balanceWeight floatValue];
//            NSInteger day = totalDose / ([timesNum integerValue] * selectedWeight);
//            weakUsageView.dayTextField.text = [NSString stringWithFormat:@"%ld", (long)(day == 0 ? 1 : day)];
//            weakOtherView.dayTextField.text = [NSString stringWithFormat:@"%ld", (long)([weakSelf.makeDays intValue] + (day == 0 ? 1 : day))];
//        }
        
        //用法用量中显示胶囊的颗粒数
        [weakSelf showCapsuleParticlesNums];
    };
    
    
    actionSheet.actionSheetViewClosed = ^{
        [weakUsageView.preDoseTextField resignFirstResponder];
    };
    
    return actionSheet;
}

- (void)showCustomCapsuleWeightPopupWithCapsuleWight:(CGFloat)dosePerUnit capsuleWeightUnit:(NSString *)doseUnit{
    
    BRAlertView *alertView = [[BRAlertView alloc] init];
    [alertView.okButton setTitle:@"保存" forState:UIControlStateNormal];
    
    __weak typeof(self) weakSelf = self;
    [alertView showCapsuleWeightAlertViewWithContentTextField:@"自定义用法用量" text:@"" capsuleWeight:dosePerUnit capsuleWeightUnit:doseUnit completion:^(BOOL isOk, NSString * _Nullable text) {
        [alertView close];
        
        if (text.length == 0 || [text isEqualToString:@""]) {
            return;
        }
        
        //将颗粒数换算为胶囊的重量
        NSInteger num = [text intValue];
        CGFloat weight = num * dosePerUnit;
        
        //更新预计服用天数
        [weakSelf updateEstimateTakeNumbersWithWeight:weight];
        
        //用法用量中显示胶囊的颗粒数
        [weakSelf showCapsuleParticlesNums];
    }];
    
}


//更新预计服用天数
- (void)updateEstimateTakeNumbersWithWeight:(CGFloat)weight {
    
    NSString *doseStr = [NSString stringWithFormat:@"%.1f", weight];
    
    //更新剂量到文本框
    _usageView.preDoseTextField.text = doseStr;
    
    // 计算预计服用天数
    NSString *timesNum = [_usageView.timesTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    
    // 保存每次多少克和预计服用天数
    NSString *drugFormmd5 = [[self.currentDrugForm md5String] substringToIndex:5];
    NSString *kOtherTypeEveryTimeAmount = [NSString stringWithFormat:@"%@_%@", kUsageOtherType_EveryTimesAmount, drugFormmd5];
    NSString *kOtherTypeEstimateDays = [NSString stringWithFormat:@"%@_%@", kUsageOtherType_EstimateDays, drugFormmd5];
    
    [Utils saveUsageType:kOtherTypeEveryTimeAmount text:doseStr withPatientId:self.patientId];
    [Utils saveUsageType:kOtherTypeEstimateDays text:timesNum withPatientId:self.patientId];
    
    if ([timesNum intValue] == 0 || weight == 0 || timesNum.length == 0) {
        // 输入无效，不进行计算
    } else {
        CGFloat totalDose = [self.balanceWeight floatValue];
        NSInteger day = totalDose / ([timesNum integerValue] * weight);
        _usageView.dayTextField.text = [NSString stringWithFormat:@"%ld", (long)(day == 0 ? 1 : day)];
        _otherView.dayTextField.text = [NSString stringWithFormat:@"%ld", (long)([self.makeDays intValue] + (day == 0 ? 1 : day))];
    }
    
}

#pragma mark - 显示胶囊克数
- (void)showCapsuleParticlesNums {
    
    BRFactoryModel *factoryModel = [_factoryArr objectAtIndex:_masterSelectedIndex];
    NSArray *factoryArr = factoryModel.list;
    BRSubFactoryModel *subFactoryModel = [factoryArr objectAtIndex:_detailSelectedIndex];
    
    // 添加验证: 颗粒剂型 + 必然甄选厂商 + 开方剂数小于7剂时的提示
    if ([factoryModel.drugFormName isEqualToString:@"颗粒"]) {
        // 判断是否为必然甄选厂商
        if ([subFactoryModel.factoryId isEqualToString:@"3"]) { // 假设必然甄选的厂商ID为3，需根据实际情况调整
            // 获取总剂数
            NSString *drugNumStr = [_usageView.drugNumTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
            int drugNum = [drugNumStr intValue];
            
            // 如果剂数小于7剂，显示提示
            if (drugNum > 0 && drugNum < 7) {
                BRAlertView *alertView = [[BRAlertView alloc] init];
                [alertView showAlertView:@"该厂商为合煎颗粒(药液浓缩后干燥)，非配方颗粒，7剂起制作(药材总量不低于800g)。" completion:^{
                    [alertView close];
                }];
                return;
            }
        }
    }
    
    //如果为胶囊，则显示当前对应的颗粒数
    if ([factoryModel.drugFormName isEqualToString:@"胶囊"]) {
        //每颗多少克
        CGFloat dosePerUnit = [subFactoryModel.dosePreUnit floatValue];
        //当前填入的是多少克
        CGFloat perDoseNum = [[_usageView.preDoseTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""] floatValue];
        //计算出 颗粒数
        NSInteger particles = perDoseNum / dosePerUnit;
        
        // 胶囊颗粒数设置已移除
    } else {
        // 胶囊颗粒数设置已移除
    }
}

#pragma mark - 获取当前的厂商
- (BRSubFactoryModel *)getCurrentSubFactoryModel {
    BRFactoryModel *factoryModel = [_factoryArr objectAtIndex:_masterSelectedIndex];
    NSArray *factoryArr = factoryModel.list;
    BRSubFactoryModel *subFactoryModel = [factoryArr objectAtIndex:_detailSelectedIndex];
    return subFactoryModel;
}

#pragma mark - 初始化代煎偏好数据
- (void)initDecoctionPreferenceData {
    // 获取当前厂商和子厂商模型
    BRFactoryModel *currentFactory = [_factoryArr objectAtIndex:_masterSelectedIndex];
    BRSubFactoryModel *currentSubFactory = [self getCurrentSubFactoryModel];
    
    // 检查是否为代煎剂型
    if (![currentFactory.drugFormName isEqualToString:@"代煎"]) {
        return;
    }
    
    NSLog(@"initDecoctionPreferenceData - 剂型: %@, 规格数量: %lu", 
          currentFactory.drugFormName ?: @"未设置", 
          (unsigned long)(currentSubFactory.packageSpecList ? currentSubFactory.packageSpecList.count : 0));
    
    // 更新当前代煎的packageSpecList
    _currentDecoctionPackageSpecList = currentSubFactory.packageSpecList;
    
    if (!_currentDecoctionPackageSpecList || _currentDecoctionPackageSpecList.count == 0) {
        // 没有规格数据时，使用默认值
        _decoctionPreferenceArray = @[];
        _selectedDecoctionPreference = @"";
        _decoctionPreferenceIndex = 0;
        NSLog(@"代煎无规格数据，使用空数组");
        return;
    }
    
    // 构建选项数组
    NSMutableArray *preferenceArray = [NSMutableArray array];
    for (BRPackageSpecModel *packageSpec in _currentDecoctionPackageSpecList) {
        if (packageSpec.value && packageSpec.value.length > 0) {
            [preferenceArray addObject:packageSpec.value];
        }
    }
    _decoctionPreferenceArray = [preferenceArray copy];
    
    BOOL hasCheckedSpec = NO;
    NSInteger defaultIndex = 0;
    
    // 检查是否有默认选中的规格（优先 checked=YES）
    for (NSInteger i = 0; i < _currentDecoctionPackageSpecList.count; i++) {
        BRPackageSpecModel *packageSpec = _currentDecoctionPackageSpecList[i];
        if (packageSpec.value && packageSpec.value.length > 0) {
            NSLog(@"检查代煎偏好: %@, checked: %@", packageSpec.value, packageSpec.checked ? @"是" : @"否");
            if (packageSpec.checked && !hasCheckedSpec) {
                hasCheckedSpec = YES;
                _decoctionPreferenceIndex = defaultIndex;
                _selectedDecoctionPreference = packageSpec.value;
                NSLog(@"使用默认选中的代煎偏好: %@", packageSpec.value);
                break;
            }
            defaultIndex++;
        }
    }
    
    // 如果没有选中的规格，使用第一个作为默认选中项
    if (!hasCheckedSpec && _decoctionPreferenceArray.count > 0) {
        _decoctionPreferenceIndex = 0;
        _selectedDecoctionPreference = _decoctionPreferenceArray.firstObject;
        NSLog(@"使用第一个代煎偏好作为默认: %@", _selectedDecoctionPreference);
    }
}

#pragma mark 获取剂型
- (void)getFactoryInfoWithType:(NSString *)type {
    
    __weak PrescriptionViewController *weakSelf = self;
    

    NSMutableArray *drugIdsArr = [[NSMutableArray alloc]initWithCapacity:0];
    for (BRSubMedicineModel *model in _drugArray) {
        [drugIdsArr addObject:model.drugId];
    }
    NSString *drugIdsString = [drugIdsArr componentsJoinedByString:@","];
    
    //请求药厂数据
    NSDictionary *dict = @{@"method_code":@"000255",
                           @"drugIds": drugIdsString,
                           @"apiVer" : @"2"
                           };
    
    MBProgressHUD *hud = nil;
    if ([type isEqualToString:@"pressAddPrescription"]) {
        hud = [Utils createLoadingHUD];
    }
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        [hud hideAnimated:YES];
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            NSLog(@"选择药剂 == %@",responseObject);
            weakSelf.factoryArr = [BRFactoryModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"list"]];
            
            for (NSInteger index=0; index<weakSelf.factoryArr.count; index++) {
                
                BRFactoryModel *facoryModel = [weakSelf.factoryArr objectAtIndex:index];
                NSArray *factoryArr = [BRSubFactoryModel mj_objectArrayWithKeyValuesArray:facoryModel.list];
                facoryModel.list = factoryArr;
                
                [weakSelf.factoryArr replaceObjectAtIndex:index withObject:facoryModel];
                
            }
            
            // 初始化代煎偏好数据（如果当前选中的是代煎剂型）
            if (weakSelf.factoryArr.count > 0 && 
                weakSelf.masterSelectedIndex < weakSelf.factoryArr.count) {
                BRFactoryModel *currentFactory = [weakSelf.factoryArr objectAtIndex:weakSelf.masterSelectedIndex];
                if ([currentFactory.drugFormName isEqualToString:@"代煎"]) {
                    [weakSelf initDecoctionPreferenceData];
                    NSLog(@"获取厂商数据后初始化代煎偏好: %@", weakSelf.selectedDecoctionPreference);
                }
            }
            
            if ([type isEqualToString:@"pressAddPrescription"]) {
                [weakSelf showSelectTypeActionSheetViewWithFactoryArr:weakSelf.factoryArr];
            }
            
        }
        else {
            
            NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
            [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
            
        }
        
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
        [hud hideAnimated:YES];
        [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
        
    }];
    
}
- (void)showSelectTypeActionSheetViewWithFactoryArr:(NSArray *)factoryArr {
    
    [self.view endEditing:YES];
    
    _selectTypeView = [[BRPresSelectTypeView alloc]init];
    _selectTypeView.masterSelectedIndex = _masterSelectedIndex;
    _selectTypeView.detailSelectedIndex = _detailSelectedIndex;
    _selectTypeView.masterArr = [NSMutableArray arrayWithArray:factoryArr];
    _selectTypeView.isShowStockInfo = NO;
    BRFactoryModel *model = [factoryArr objectAtIndex:_masterSelectedIndex];
    [_selectTypeView show];
    
    __weak PrescriptionViewController *weakSelf = self;
    _selectTypeView.pressDetailRow = ^(NSInteger masterSelectedIndex,NSInteger detailSelectedIndex){
        
        [weakSelf.selectTypeView close];
        
        weakSelf.masterSelectedIndex = masterSelectedIndex;
        weakSelf.detailSelectedIndex = detailSelectedIndex;
        
        BRFactoryModel *model = [factoryArr objectAtIndex:masterSelectedIndex];
        NSArray *detailArr = model.list;
        BRSubFactoryModel *subModel = [detailArr objectAtIndex:detailSelectedIndex];
        
        // 先更新剂型，再更新规格信息和辅料信息
        weakSelf.usageView.drugForm = model.drugFormName;
        [weakSelf.usageView updateSpecificationWithFactoryModel:subModel];
        [weakSelf.usageView updateAuxiliaryMaterialWithFactoryModel:subModel];
        
        // 如果是代煎剂型，更新代煎偏好数据
        if ([model.drugFormName isEqualToString:@"代煎"]) {
            [weakSelf initDecoctionPreferenceData];
            // 更新UI显示
            if (weakSelf.selectedDecoctionPreference.length > 0) {
                weakSelf.otherView.decoctionPreference = weakSelf.selectedDecoctionPreference;
            }
            NSLog(@"厂商切换 - 更新代煎偏好数据: %@", weakSelf.selectedDecoctionPreference);
        }
        
        if (_patientArray.count == 0 && self.prescribeType == BRPrescribeTypeNormal) {
            [weakSelf.view makeToast:@"请选择患者" duration:kToastDuration position:CSToastPositionCenter];
            //[weakSelf getUserInfoWithType:BRGetPatientInfoTypeFirst];
            return ;
        }

         AddDrugViewController *addDrugVC = [[AddDrugViewController alloc]init];
        [addDrugVC setTitleWithFactoryArray:factoryArr masterSelectedIndex:masterSelectedIndex detailSelectedIndex:detailSelectedIndex];
         addDrugVC.patientId = weakSelf.patientId;
        addDrugVC.patientModel = weakSelf.patientModel;
        addDrugVC.prescribeType = weakSelf.prescribeType;
        addDrugVC.takerIsPregnant = weakSelf.patient_is_pregnanet;
        addDrugVC.delegate = weakSelf;
         [weakSelf.navigationController pushViewController:addDrugVC animated:YES];
        
    };
    
    __weak BRPresSelectTypeView *weakSelectTypeView = _selectTypeView;
    _selectTypeView.gotoInfo = ^(NSString *urlStr){
        
        [weakSelectTypeView close];
        
        FactoryInfoViewController *factoryInfoVC = [[FactoryInfoViewController alloc]init];
        factoryInfoVC.urlStr = [urlStr stringByTrim];
        [weakSelf.navigationController pushViewController:factoryInfoVC animated:YES];
        
    };
    
}

#pragma mark creat 医案

- (BOOL)checkFillQuickPatientInfo {
    //真实姓名
    if (self.patientName.length == 0) {
        [self.view makeToast:@"请输入患者的真实姓名" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    
    //合法姓名
    if (![Utils isChineseName:self.patientName]) {
        NSString *text = @"根据《处方管理法》规定，开方时需要填写真实姓名，请跟患者确认真实姓名。";
        
        BRAlertView *alertView = [[BRAlertView alloc] init];
        [alertView showAlertView:text completion:^{
            [alertView close];
        }];
        
        return NO;
    }
    
    if (self.patientAge.length == 0) {
        [self.view makeToast:@"请输入患者的真实年龄" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    
    
    if (self.patientSex.length == 0) {
        [self.view makeToast:@"请选择患者的性别" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    
    //如果患者性别为女 并且未选择怀孕状态
    if(![self.patientSex isEqualToString:@"1"] && ![self.writePatientInfoView choosePregnant]){
        [self.view makeToast:@"请选择该患者是否怀孕" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    
    return YES;
}


// 添加: 辅助方法用于验证字符串是否为有效数字
- (BOOL)isValidNumber:(NSString *)string {
    NSScanner *scanner = [NSScanner scannerWithString:string];
    double val;
    return [scanner scanDouble:&val] && scanner.isAtEnd;
}

- (void)pressToCreate {
    
    //判断用户信息是否填写
    if ((self.prescribeType == BRPrescribeTypeQuick || self.prescribeType == BRPrescribeTypeMessage) &&  ![self checkFillQuickPatientInfo]) {
        return;
    }
    
    if (_drugArray.count == 0) {
        
        [self.view makeToast:@"请添加药材" duration:kToastDuration position:CSToastPositionCenter];
        return;
        
    }
    
    NSString *diagnosesString = [_diagnosesView.diagnosesTextView.internalTextView.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    
    if (diagnosesString.length == 0) {
        
        [self.view makeToast:@"请添加辨证" duration:kToastDuration position:CSToastPositionCenter];
        return;
        
    }
    
    if (_factoryArr.count == 0) {
        return;
    }
    
    BRFactoryModel *factoryModel = [_factoryArr objectAtIndex:_masterSelectedIndex];
    NSArray *factoryArr = factoryModel.list;
    BRSubFactoryModel *subFactoryModel = [factoryArr objectAtIndex:_detailSelectedIndex];
    
    // 颗粒，饮片，代煎，外用中药这几种剂型，总剂数最大值为99
    if ([factoryModel.drugFormName isEqualToString:@"颗粒"] ||
        [factoryModel.drugFormName isEqualToString:@"饮片"] ||
        [factoryModel.drugFormName isEqualToString:@"代煎"] ||
        [factoryModel.drugFormName isEqualToString:@"外用中药"]) {
        
        NSString *drugNumStr = [_usageView.drugNumTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        int drugNum = [drugNumStr intValue];
        
        if (drugNum > 99) {
            BRAlertView *alertView = [[BRAlertView alloc] init];
            [alertView showAlertView:@"总剂数最大为99，已自动修改为99剂" completion:^{
                [alertView close];
            }];
            _usageView.drugNumTextField.text = @"99";
            return;
        }
    }
    
    // 添加验证: 代煎剂型下的剂数限制
    if ([factoryModel.drugFormName isEqualToString:@"代煎"]) {
        NSString *drugNumStr = [_usageView.drugNumTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        int drugNum = [drugNumStr intValue];
        
        // 青庐药局【精品选货】厂商的总剂数最小为5
        if ([subFactoryModel.factoryName containsString:@"青庐药局"]) {
            if (drugNum > 0 && drugNum < 5) {
                NSString *message = [NSString stringWithFormat:@"%@5剂起煎，已自动修改为 5 剂。", subFactoryModel.factoryName];
                BRAlertView *alertView = [[BRAlertView alloc] init];
                [alertView showAlertView:message completion:^{
                    [alertView close];
                }];
                _usageView.drugNumTextField.text = @"5";
                return;
            }
        }
        // 其他厂商的总剂数最小为3
        else {
            if (drugNum > 0 && drugNum < 3) {
                NSString *message = [NSString stringWithFormat:@"%@3剂起煎，已自动修改为 3 剂。", subFactoryModel.factoryName];
                BRAlertView *alertView = [[BRAlertView alloc] init];
                [alertView showAlertView:message completion:^{
                    [alertView close];
                }];
                _usageView.drugNumTextField.text = @"3";
                return;
            }
        }
    }
    
    // 添加验证: 颗粒剂型 + 必然甄选厂商 + 开方剂数小于7剂时的提示
    if ([factoryModel.drugFormName isEqualToString:@"颗粒"]) {
        // 判断是否为必然甄选厂商
        if ([subFactoryModel.factoryName containsString:@"必然甄选"]) { // 通过厂商名称判断
            // 获取总剂数
            NSString *drugNumStr = [_usageView.drugNumTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
            int drugNum = [drugNumStr intValue];
            
            // 如果剂数小于7剂，显示提示
            if (drugNum > 0 && drugNum < 7) {
                BRAlertView *alertView = [[BRAlertView alloc] init];
                [alertView showAlertView:@"该厂商为合煎颗粒(药液浓缩后干燥)，非配方颗粒，7剂起制作(药材总量不低于800g)。" completion:^{
                    [alertView close];
                }];
                return;
            }
        }
    }
    
    //饮片中包含 【生姜】无法开方
    if([factoryModel.drugFormName isEqualToString:@"饮片"]){
        //是否代煎
//        BOOL isMakeCost = self.otherView.makeCostSwitch.isOn;
        
//        //不使用药房代煎
//        if(!isMakeCost){
            //是否包含生姜
            BOOL isHasSJ = NO;
            
            for (int i = 0; i < self.drugArray.count; i++) {
                BRSubMedicineModel *model = [self.drugArray objectAtIndex:i];
                
                if([model.drugName hasPrefix:@"生姜"]){
                    isHasSJ = YES;
                    break;
                }
            }
            
            //不使用药房代煎，并且包含生姜
            if(isHasSJ){
                
                [Utils br_newShowAlertViewMessage:@"生姜属于易霉药材，建议代煎或患者自备" completion:nil];
                
                return;
            }
            
//        }
    }
    
    
    //药材总重
    if (!_balanceWeight ||_balanceWeight.length == 0) {
        _balanceWeight = @"";
    }
    
    
    if (![factoryModel.drugFormName isEqualToString:@"颗粒"] &&
        ![factoryModel.drugFormName isEqualToString:@"饮片"] &&
        ![factoryModel.drugFormName isEqualToString:@"代煎"] &&
        ![factoryModel.drugFormName isEqualToString:@"外用中药"]) {
        
        //非颗粒与饮片部分
        //每日几次
        NSString *timesStr = [_usageView.timesTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        //每次几g
        NSString *preDoseStr = [_usageView.preDoseTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        //预计服用几天
        NSString *dayStr = [_usageView.dayTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        
        if ([timesStr integerValue] == 0 ||
            [preDoseStr integerValue] == 0 ||
            [dayStr integerValue] == 0 ||
            timesStr.length == 0 ||
            preDoseStr.length == 0 ||
            dayStr.length == 0) {
            
            [self.view makeToast:@"请添加用法用量" duration:kToastDuration position:CSToastPositionCenter];
            return;
            
        }
        
        /*
        if ([timesStr floatValue] *[preDoseStr floatValue] > [balanceWeight floatValue]) {
            
            [self.view makeToast:@"" duration:kToastDuration position:CSToastPositionCenter];
            return;
        }
         */
        
    }
    else {
        
        //颗粒与饮片部分
        NSString *drugNumStr = [_usageView.drugNumTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        //每日几剂
        NSString *usageStr = [_usageView.usageTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        //每剂分几次服用
        NSString *timesStr = [_usageView.timesTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        
        if ([drugNumStr integerValue] == 0 || [usageStr integerValue] == 0 || [timesStr integerValue] == 0 || drugNumStr.length == 0 || usageStr.length == 0 || timesStr.length == 0) {
            
            [self.view makeToast:@"请添加用法用量" duration:kToastDuration position:CSToastPositionCenter];
            return;
            
        }
        
        if ([drugNumStr integerValue]<[usageStr integerValue]) {
            [self.view makeToast:@"每日剂数需小于等于总剂数" duration:kToastDuration position:CSToastPositionCenter];
            return;
        }
        
    }
    
    //用药方法
    if ([factoryModel.drugFormName isEqualToString:@"饮片"] || [factoryModel.drugFormName isEqualToString:@"代煎"] || [factoryModel.drugFormName isEqualToString:@"散剂"]) {
        if (_usageView.selectedMode.length == 0) {
            [self.view makeToast:@"请选择用药方法" duration:2 position:CSToastPositionCenter];
            return;
        }
    }
    
    // 膏方剂型辅料必选检查
    if ([factoryModel.drugFormName isEqualToString:@"膏方"] && _usageView.auxiliaryMaterialSelectorView.superview && !_usageView.auxiliaryMaterialSelectorView.superview.hidden) {
        // 检查是否有"不添加辅料"选项
        BOOL hasNoMaterialOption = NO;
        if (subFactoryModel.makeMaterialList && [subFactoryModel.makeMaterialList containsObject:@"不添加辅料"]) {
            hasNoMaterialOption = YES;
        }
        
        if (hasNoMaterialOption) {
            // 有"不添加辅料"选项时，必须选择辅料或选择不添加辅料
            if (_usageView.selectedAuxiliaryMaterials.count == 0 && !_usageView.isNoAuxiliaryMaterial) {
                [self.view makeToast:@"请选择辅料或选择不添加辅料" duration:kToastDuration position:CSToastPositionCenter];
                return;
            }
        } else {
            // 没有"不添加辅料"选项时，必须选择至少一个辅料
            if (_usageView.selectedAuxiliaryMaterials.count == 0) {
                [self.view makeToast:@"请选择辅料" duration:kToastDuration position:CSToastPositionCenter];
                return;
            }
        }
    }
    
    //复诊时间
    NSString *dayStr = [_otherView.dayTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    if (_otherView.swich.isOn == YES && ([dayStr integerValue] == 0 || dayStr.length == 0) ) {
        
        [self.view makeToast:@"请添加复诊时间" duration:kToastDuration position:CSToastPositionCenter];
        return;
        
    }
    
    if (_otherView.saveButton.isSelected) {
        
        NSString *nameStr = [_otherView.nameTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        if (nameStr.length == 0) {
            
            [self.view makeToast:@"请添加模板名称" duration:kToastDuration position:CSToastPositionCenter];
            return;
            
        }
        
    }
    
    if (!_offlineView.offlineButton.isSelected) {
        
        [self.view makeToast:@"请确认患者已进行过线下诊断" duration:kToastDuration position:CSToastPositionCenter];
        return;
        
    }
    
    //补充说明
    NSString *descStr = [_otherView.descTextView.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    if (descStr.length == 0) {
        descStr = @"";
    }
    
    //按语
    NSString *noteStr = [_noteView.textView.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    if (noteStr.length == 0) {
        noteStr = @"";
    }
    
    NSString *diagnosesStr = [_diagnosesView.diagnosesTextView.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    if (diagnosesStr.length == 0) {
        diagnosesStr = @"";
    }
    
    //共几付
    NSString *drugNum = @"";
    //每日几付
    NSString *dayPreNum = @"";
    //每付几次
    NSString *preTimes = @"";
    //每日几次
    NSString *mrjc = @"";
    //每次几克
    NSString *mcjk = @"";
    //预计可用几天
    NSString *takeDays = @"";
    //制作费
    NSString *makeCost = @"";
    if (![factoryModel.drugFormName isEqualToString:@"颗粒"] &&
            ![factoryModel.drugFormName isEqualToString:@"饮片"] &&
            ![factoryModel.drugFormName isEqualToString:@"代煎"] &&
            ![factoryModel.drugFormName isEqualToString:@"外用中药"]) {
            mrjc = _usageView.timesTextField.text;
            
            // mcjk字段应该存储重量值（克数），而非包装数量
            if ([factoryModel.drugFormName isEqualToString:@"膏方"]) {
                if (_usageView.isCreamFormulaBottlePackage) {
                    // 瓶装膏方：直接使用输入的重量值
                    mcjk = [NSString stringWithFormat:@"%.1f", _usageView.creamFormulaDirectWeightValue];
                } else {
                    // 非瓶装膏方：计算总重量 = 包装数量 × 每单位重量
                    double totalWeight = _usageView.selectedCreamFormulaUnitCount * _usageView.currentCreamFormulaSpecGramPerUnit;
                    mcjk = [NSString stringWithFormat:@"%.1f", totalWeight];
                }
            } else if ([factoryModel.drugFormName isEqualToString:@"蜜丸"]) {
                // 蜜丸：计算总重量 = 包装数量 × 每单位重量
                double totalWeight = _usageView.selectedUnitCount * _usageView.currentSpecGramPerUnit;
                mcjk = [NSString stringWithFormat:@"%.1f", totalWeight];
            } else if ([factoryModel.drugFormName isEqualToString:@"胶囊"]) {
                // 胶囊：计算总重量 = 包装数量 × 每单位重量
                double totalWeight = _usageView.selectedCapsuleUnitCount * _usageView.currentCapsuleSpecGramPerUnit;
                mcjk = [NSString stringWithFormat:@"%.1f", totalWeight];
            } else if ([factoryModel.drugFormName isEqualToString:@"水丸"]) {
                // 水丸：计算总重量 = 包装数量 × 每单位重量
                double totalWeight = _usageView.selectedWaterPillUnitCount * _usageView.currentWaterPillSpecGramPerUnit;
                mcjk = [NSString stringWithFormat:@"%.1f", totalWeight];
            } else {
                // 散剂或其他剂型：直接使用输入框的重量值
                mcjk = _usageView.preDoseTextField.text;
            }
            
            takeDays = _usageView.dayTextField.text;
            makeCost = [[_chargeView.productionCostsLabel.text stringByReplacingOccurrencesOfString:@"¥" withString:@""] stringByReplacingOccurrencesOfString:@" " withString:@""];
        }
    else {
        drugNum = _usageView.drugNumTextField.text;
        dayPreNum = _usageView.usageTextField.text;
        preTimes = _usageView.timesTextField.text;
        
        // 修改: 添加对颗粒、代煎、外用中药的制作费用处理
        if ([factoryModel.drugFormName isEqualToString:@"代煎"] ||
            [factoryModel.drugFormName isEqualToString:@"颗粒"] ||
            [factoryModel.drugFormName isEqualToString:@"外用中药"]) {
            
            // 修改: 增加安全性检查
            NSString *costText = _chargeView.productionCostsLabel.text;
            if (costText.length > 0 && [costText containsString:@"="]) {
                // 有 "=" 时的处理
                makeCost = [[[costText componentsSeparatedByString:@"="] lastObject] stringByTrim];
                // 修改: 增加额外的数值检查
                if (makeCost.length == 0 || ![self isValidNumber:makeCost]) {
                    makeCost = @"0.00";
                }
            } else if (costText.length > 0) {
                // 无 "=" 但有值时的处理
                makeCost = [[costText stringByReplacingOccurrencesOfString:@"¥" withString:@""] stringByTrim];
                if (makeCost.length == 0 || ![self isValidNumber:makeCost]) {
                    makeCost = @"0.00";
                }
            } else {
                // 完全没有值的处理
                makeCost = @"0.00";
            }
        }
    }
    
    //诊费
    NSString *consultationfee = [_chargeView.chargeTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    if (consultationfee.length == 0) {
        consultationfee = @"0";
    }
    
    //服药禁忌
    NSString *contraindication = _otherView.contraindicationButton.titleLabel.text;
    if ([contraindication isEqualToString:@"选择服药禁忌"]) {
        contraindication = @"";
    }
    
    //是否保存处方
    NSString *isSaveTemplate = @"0";
    if (_otherView.saveButton.isSelected) {
        isSaveTemplate = @"1";
    }
    else {
        isSaveTemplate = @"0";
    }
    
    //是否保密
    NSString *isSecrecy = @"0";
    if (_otherView.secretButton.isSelected) {
        isSecrecy = @"1";
    }
    else if (_otherView.secretDoseButton.isSelected){
        isSecrecy = @"2";   //剂量保密
    }
    else {
        isSecrecy = @"0";
    }
    
    NSMutableArray *drugArr = [[NSMutableArray alloc]init];
    NSMutableArray *detailsArr = [[NSMutableArray alloc]init];
    for (NSInteger index=0; index<_drugArray.count; index++) {
        BRSubMedicineModel *model = [_drugArray objectAtIndex:index];
        
        NSString *useMethod = @"";
        if (model.useMethod) {
            useMethod = model.useMethod;
        }
        
        NSDictionary *dict = @{
                               @"dose":model.dose,
                               @"drugId":model.drugId,
                               @"drugName":model.drugName,
                               @"index":[NSString stringWithFormat:@"%ld",index],
                               @"unit":model.unit,
                               @"useMethod":useMethod
                               };
        
        NSMutableDictionary *detailDict = [NSMutableDictionary dictionaryWithDictionary:@{
            @"amount":model.dose,
            @"name":model.drugName,
            @"type":useMethod,
            @"unit":model.unit,
//                                     @"recipelSalePrice":finalPrice
        }];
        
        if (model.recipelSalePrice) {
            [detailDict setValue:model.recipelSalePrice forKey:@"recipelSalePrice"];
        }
        
        //price后面计算不需要除以0.7
        if (model.price) {
            [detailDict setValue:model.price forKey:@"price"];
        }
        
        [drugArr addObject:dict];
        [detailsArr addObject:detailDict];
        
    }
    
    //多少天后发送问诊单
    NSString *sendAfterDay = [_otherView.dayTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    
    //模板名称
    NSString *templateName = @"";
    if (_otherView.saveButton.isSelected) {
        templateName = [_otherView.nameTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    }
    
    NSString *costDesc = @"";
    if (subFactoryModel.costDesc) {
//        costDesc = [subFactoryModel.costDesc stringByURLEncode];
        costDesc = subFactoryModel.costDesc;
    }
    
    //医技服务费
    NSString *medicalServiceFee = @"0";
    if (![_serviceProportion isEqualToString:_defaultServiceProportion]) {
        medicalServiceFee = [_chargeView.serviceTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        if (!medicalServiceFee || medicalServiceFee.length == 0) {
            medicalServiceFee = @"0";
        }
    }
    else {
        
        NSDecimalNumber *drugPriceDecimal = [NSDecimalNumber decimalNumberWithString:@"0"];
        
        if (![factoryModel.drugFormName isEqualToString:@"颗粒"] &&
            ![factoryModel.drugFormName isEqualToString:@"饮片"] &&
            ![factoryModel.drugFormName isEqualToString:@"代煎"] &&
            ![factoryModel.drugFormName isEqualToString:@"外用中药"]) {
            
            drugPriceDecimal = [NSDecimalNumber decimalNumberWithString:_drugPrice];
            
        }else {
            NSDecimalNumber *drugNumDecimal = [NSDecimalNumber decimalNumberWithString:_usageView.drugNumTextField.text];
            drugPriceDecimal = [[NSDecimalNumber decimalNumberWithString:_drugPrice] decimalNumberByMultiplyingBy:drugNumDecimal];
        }
       
        NSDecimalNumber *proportionDecimal = [NSDecimalNumber decimalNumberWithString:_serviceProportion];
        
        // 防止除零溢出：当比例接近1时，分母接近0会导致溢出
        NSDecimalNumber *denominator = [[NSDecimalNumber decimalNumberWithString:@"1"] decimalNumberBySubtracting:proportionDecimal];
        NSDecimalNumber *serviceChargeDecimal;
        
        // 如果分母过小（接近0），则设置一个安全的默认值
        if ([denominator compare:[NSDecimalNumber decimalNumberWithString:@"0.001"]] == NSOrderedAscending) {
            // 当比例过高时，使用一个合理的上限值
            serviceChargeDecimal = [drugPriceDecimal decimalNumberByMultiplyingBy:[NSDecimalNumber decimalNumberWithString:@"999"]];
        } else {
            serviceChargeDecimal = [[drugPriceDecimal decimalNumberByMultiplyingBy:proportionDecimal] decimalNumberByDividingBy:denominator];
        }
        
        medicalServiceFee = [NSString stringWithFormat:@"%@",serviceChargeDecimal];
        
    }
     /*
    NSString *medicalServiceFee = [_chargeView.serviceTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    if (!medicalServiceFee || medicalServiceFee.length == 0) {
        medicalServiceFee = @"0";
    }
     */
    
    //是否发送复诊单
    NSString *isAutoSend = @"1";
    if (_otherView.swich.isOn) {
        isAutoSend = @"1";
    }
    else {
        isAutoSend = @"0";
    }
    
#pragma mark 药方参数
    NSMutableDictionary *orderDict = [NSMutableDictionary dictionaryWithDictionary:@{
                                @"balanceWeight":_balanceWeight,
                                @"consultationfee":consultationfee,
                                @"contraindication":contraindication,
                                @"dayPreNum":dayPreNum,
                                @"description":diagnosesStr,
                                @"drugForm":factoryModel.drugFormName,
                                @"drugPrice":@"",
                                @"instructions":descStr,
                                @"isSaveTemplate":isSaveTemplate,
                                @"isSecrecy":isSecrecy,
                                @"makeDescription":costDesc,
                                @"mcjk":mcjk,
                                @"mrjc":mrjc,
                                @"note":noteStr,
//                                @"patientId":_patientId,
                                @"preDetailList":drugArr,
                                @"preTimes":preTimes,
                                @"productSubType":subFactoryModel.productSubType,
                                @"providerId":subFactoryModel.factoryId,
                                @"sendAfterDay":sendAfterDay,
                                @"takeDays":takeDays,
//                                @"takerAge":_patientModel.age,
//                                @"takerId":_patientModel.patientId,
//                                @"takerIsPregnant":_patientModel.isPregnant,
//                                @"takerName":_patientModel.name,
//                                @"takerSex":_patientModel.sex,
                                @"templateName":templateName,
                                @"totalPreNum":drugNum,
                                @"useTime":_usageView.changeTimeButton.titleLabel.text,
                                @"makeCost":makeCost,
                                @"makeDays":_makeDays,
                                @"medicalServiceFee":medicalServiceFee,
                                @"isAutoSend":isAutoSend,
                                @"mode":_usageView.selectedMode ? _usageView.selectedMode : @"",
                                @"packageSpec":_usageView.selectedSpecification ? _usageView.selectedSpecification : @"",
                                @"specGramPerUnit":@(_usageView.currentSpecGramPerUnit),
                                @"specUnit":_usageView.currentSpecUnit ? _usageView.currentSpecUnit : @"",
                                @"specWeightUnit":_usageView.currentSpecWeightUnit ? _usageView.currentSpecWeightUnit : @"",
                                @"selectedUnitCount":@(_usageView.selectedUnitCount),
                                }];
    
    //代煎 制作方法
    if ([factoryModel.drugFormName isEqualToString:@"代煎"]) {
        //选择的制作偏好
        NSString *makeMethod = self.selectedDecoctionPreference.length > 0 ? self.selectedDecoctionPreference : @"";
        [orderDict setValue:makeMethod forKey:@"makeMethod"];
        
        // 同时提交到 packageSpec 字段
        [orderDict setValue:makeMethod forKey:@"packageSpec"];
        
        // 设置代煎剂型的 mcDoseUnit 和 mcDose
        [orderDict setValue:@"袋" forKey:@"mcDoseUnit"];
        [orderDict setValue:@(1) forKey:@"mcDose"];
        
        NSLog(@"代煎提交数据 - makeMethod: %@, packageSpec: %@, mcDose: 1, mcDoseUnit: 袋", makeMethod, makeMethod);
    }
    
    //胶囊 每次服用剂量
    if ([factoryModel.drugFormName isEqualToString:@"胶囊"]) {
        // 胶囊：直接提交包装数量（与蜀丸、水丸保持一致）
        [orderDict setValue:@(_usageView.selectedCapsuleUnitCount) forKey:@"mcDose"];
        [orderDict setValue:_usageView.currentCapsuleSpecUnit forKey:@"mcDoseUnit"];
        NSLog(@"胶囊提交数据 - mcDose: %ld, mcDoseUnit: %@", (long)_usageView.selectedCapsuleUnitCount, _usageView.currentCapsuleSpecUnit);
    }
    
    //蜜丸 每次服用剂量
    if ([factoryModel.drugFormName isEqualToString:@"蜜丸"]) {
        // 蜜丸：直接提交包装数量
        [orderDict setValue:@(_usageView.selectedUnitCount) forKey:@"mcDose"];
        [orderDict setValue:_usageView.currentSpecUnit forKey:@"mcDoseUnit"];
        NSLog(@"蜜丸提交数据 - mcDose: %ld, mcDoseUnit: %@", (long)_usageView.selectedUnitCount, _usageView.currentSpecUnit);
    }
    
    //水丸 每次服用剂量
    if ([factoryModel.drugFormName isEqualToString:@"水丸"]) {
        // 水丸：直接提交包装数量
        [orderDict setValue:@(_usageView.selectedWaterPillUnitCount) forKey:@"mcDose"];
        [orderDict setValue:_usageView.currentWaterPillSpecUnit forKey:@"mcDoseUnit"];
        NSLog(@"水丸提交数据 - mcDose: %ld, mcDoseUnit: %@", (long)_usageView.selectedWaterPillUnitCount, _usageView.currentWaterPillSpecUnit);
    }
    
    //膏方 每次服用剂量
    if ([factoryModel.drugFormName isEqualToString:@"膏方"]) {
        if (_usageView.isCreamFormulaBottlePackage) {
            // 瓶装膏方：将输入的重量转换为包装数量（瓶数）
            double bottleCount = _usageView.creamFormulaDirectWeightValue / _usageView.currentCreamFormulaSpecGramPerUnit;
            [orderDict setValue:@(bottleCount) forKey:@"mcDose"];
            [orderDict setValue:_usageView.currentCreamFormulaSpecUnit forKey:@"mcDoseUnit"];
            NSLog(@"瓶装膏方提交数据 - 输入重量: %.1f%@, 计算瓶数: %.3f, mcDose: %.3f, mcDoseUnit: %@", 
                  _usageView.creamFormulaDirectWeightValue, _usageView.currentCreamFormulaSpecWeightUnit, 
                  bottleCount, bottleCount, _usageView.currentCreamFormulaSpecUnit);
        } else {
            // 非瓶装膏方：直接提交包装数量
            [orderDict setValue:@(_usageView.selectedCreamFormulaUnitCount) forKey:@"mcDose"];
            [orderDict setValue:_usageView.currentCreamFormulaSpecUnit forKey:@"mcDoseUnit"];
            NSLog(@"非瓶装膏方提交数据 - mcDose: %ld, mcDoseUnit: %@", (long)_usageView.selectedCreamFormulaUnitCount, _usageView.currentCreamFormulaSpecUnit);
        }
        
        // 添加辅料信息
        if (_usageView.isNoAuxiliaryMaterial) {
            [orderDict setValue:@"不添加辅料" forKey:@"makeMaterial"];
        } else if (_usageView.selectedAuxiliaryMaterials.count > 0) {
            NSString *auxiliaryMaterialString = [_usageView.selectedAuxiliaryMaterials componentsJoinedByString:@","];
            [orderDict setValue:auxiliaryMaterialString forKey:@"makeMaterial"];
        }
    }
    
    //快速开方
    if (self.prescribeType == BRPrescribeTypeQuick || self.prescribeType == BRPrescribeTypeMessage) {
        [orderDict setValuesForKeysWithDictionary:@{
            @"takerAge":self.patientAge,
            @"takerIsPregnant":self.patient_is_pregnanet,
            @"takerName":self.patientName,
            @"takerSex":self.patientSex
        }];
    }
    //正常开方
    else {
        [orderDict setValuesForKeysWithDictionary:@{
            @"takerAge":_patientModel.age,
            @"takerId":_patientModel.patientId,
            @"takerIsPregnant":_patientModel.isPregnant,
            @"takerName":_patientModel.name,
            @"takerSex":_patientModel.sex,
            @"patientId":_patientId,
        }];
    }
    
    
    NSString *totalPriceStr = [[[_chargeView.totalPriceLabel.text componentsSeparatedByString:@"¥"] lastObject] stringByReplacingOccurrencesOfString:@" " withString:@""];
    
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc]init];
    dateFormatter.dateFormat = @"yyyy-MM-dd HH:mm";
    NSMutableDictionary *infoDict = [NSMutableDictionary dictionaryWithDictionary:@{
//                               @"age":_patientModel.age,
                               @"amount":totalPriceStr,
                               @"contraindication":contraindication,
                               @"totalCount":drugNum,
                               @"details":detailsArr,
                               @"dialectical":diagnosesStr,
                               @"eatTime":_usageView.changeTimeButton.titleLabel.text,
                               @"drugForm":factoryModel.drugFormName,
                               @"mrjc":mrjc,
                               @"mcjk":mcjk,
                               @"preUsage":dayPreNum,
                               @"preDays":preTimes,
                               @"dcId":subFactoryModel.factoryId,
                               @"productProviteName":subFactoryModel.factoryName,
                               @"id":@"",
//                               @"ispregnant":_patientModel.isPregnant,
                               @"mode":_usageView.selectedMode ? _usageView.selectedMode : @"",
//                               @"name":_patientModel.name,
                               @"remak":descStr,
//                               @"sex":_patientModel.sex,
                               @"doctorName":[[UserManager shareInstance] getUserName],
                               @"creatTime":[dateFormatter stringFromDate:[NSDate date]],
                               @"teckDays":takeDays,
                               @"medicalServiceFee":medicalServiceFee,
                               @"isAutoSend":isAutoSend,
                               @"isDaiJian" : _isUsePharmacyMake == YES ? @"1" : @"0",
                               @"packageSpec":_usageView.selectedSpecification ? _usageView.selectedSpecification : @"",
                               @"specGramPerUnit":@(_usageView.currentSpecGramPerUnit),
                               @"specUnit":_usageView.currentSpecUnit ? _usageView.currentSpecUnit : @"",
                               @"specWeightUnit":_usageView.currentSpecWeightUnit ? _usageView.currentSpecWeightUnit : @"",
                               @"selectedUnitCount":@(_usageView.selectedUnitCount)
                               }];
    
    if (self.prescribeType == BRPrescribeTypeQuick || self.prescribeType == BRPrescribeTypeMessage) {
        [infoDict setValuesForKeysWithDictionary:@{
            @"age":self.patientAge,
            @"ispregnant":self.patient_is_pregnanet,
            @"name":self.patientName,
            @"sex":self.patientSex,
        }];
    }
    else{
        [infoDict setValuesForKeysWithDictionary:@{
            @"age":_patientModel.age,
            @"ispregnant":_patientModel.isPregnant,
            @"name":_patientModel.name,
            @"sex":_patientModel.sex,
        }];
    }

    if ([medicalServiceFee isEqualToString:@"0"] && _generatClickNumber == 0) {
        
        BRAlertView *alerView = [[BRAlertView alloc] init];
        [alerView.okButton setTitle:@"确认" forState:UIControlStateNormal];
        [alerView.cancelButton setTitle:@"" forState:UIControlStateNormal];
        
        [alerView showAlertView:@"医技服务费输入为0元，表示此订单您的奖励为0元，请慎重修改！" completion:^{
            [alerView close];
            _generatClickNumber = 1;
        }];
        
    } else {
        
        if (templateName.length) {
            //判断常用方名称是否已经存在
            NSDictionary *templateDict = @{
                                           @"templateName":templateName,
                                           @"list":[NSArray array],
                                           @"userId":[UserManager shareInstance].getUserId,
                                           @"templateId":@""
                                           };
            NSString *templateStr = [templateDict mj_JSONString];
            
            NSDictionary *requestDict = @{
                                          @"method_code":@"000220",
                                          @"template":templateStr
                                          };
            
            MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:nil];
            __weak typeof(progressHUD) hud = progressHUD;
            [HTTPRequest POST:kServerDomain parameters:requestDict progress:^(NSProgress *progress) {
                
            } success:^(NSURLSessionDataTask *task, id responseObject) {
                
                hud.hidden = YES;
                
                NSString *code = [responseObject objectForKey:@"code"];
                if ([code isEqualToString:@"0000"]) {
                    
                    [self gotoMedicatedInfoVCWithDataDic:infoDict orderDic:orderDict];
                    
                }
                else {
                    
                    NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
                    [self.view makeToast:errorMsgStr duration:kToastDuration position:CSToastPositionCenter];
                    
                }
                
                
            } failure:^(NSURLSessionDataTask *task, NSError *error) {
                
                hud.hidden = YES;
                [self.view makeToast:@"请求失败，请稍后再试" duration:kToastDuration position:CSToastPositionCenter];
                
            }];
            
        }
        else {
            [self gotoMedicatedInfoVCWithDataDic:infoDict orderDic:orderDict];
        }
        
    }
}

- (void)gotoMedicatedInfoVCWithDataDic:(NSDictionary *)dataDict orderDic:(NSDictionary *)orderDic {
    
//    // 修改传递参数,增加制作费显示判断
//    BRFactoryModel *factoryModel = [_factoryArr objectAtIndex:_masterSelectedIndex];
//    
//    if (([factoryModel.drugFormName isEqualToString:@"颗粒"] ||
//         [factoryModel.drugFormName isEqualToString:@"外用中药"]) &&
//        _makeCost.length > 0 && ![_makeCost isEqualToString:@"0"]) {
//        
//        NSMutableDictionary *newOrderDic = [orderDic mutableCopy];
//        [newOrderDic setObject:_makeCost forKey:@"makeCost"];
//        orderDic = newOrderDic;
//    }
    
    
    MedicatedInfoViewController *medicatedInfoVC = [[MedicatedInfoViewController alloc]init];
    medicatedInfoVC.medicatedInfoOrPreview = NSMedicatedPreview;
    medicatedInfoVC.dataDic = dataDict;
    medicatedInfoVC.orderDict = orderDic;
    medicatedInfoVC.prescribeType = self.prescribeType;
    [self.navigationController pushViewController:medicatedInfoVC animated:YES];
    
    __weak  PrescriptionViewController *weakSelf = self;
    medicatedInfoVC.sendSuccessBlock = ^{
        
        [weakSelf sendPrescriptionSuccess];
        
    };
    
}
//药方发送成功
- (void)sendPrescriptionSuccess {
    
    //快速开方发送成功   ？？？是否进行区分
    if (self.prescribeType == BRPrescribeTypeQuick || self.prescribeType == BRPrescribeTypeMessage) {
        
        //更新保存的用量数据
        NSString *drugFormMd5 = [[self.currentDrugForm md5String] substringToIndex:5];
        
        NSString *kandYTypeAll = [NSString stringWithFormat:@"%@_%@",kUsageKandYType_All, self.quickOrderTempId];
        NSString *kandYTypeEveryDayAmount = [NSString stringWithFormat:@"%@_%@",kUsageKandYType_EveryDayAmount, self.quickOrderTempId];
        NSString *kandYTypeTimes = [NSString stringWithFormat:@"%@_%@",kUsageKandYType_times, self.quickOrderTempId];
        
        
        NSString *otherTypeEveryDayTimes = [NSString stringWithFormat:@"%@_%@_%@",kUsageOtherType_EveryDayTimes, drugFormMd5, self.quickOrderTempId];
        NSString *otherTypeEveryTimesAmount = [NSString stringWithFormat:@"%@_%@_%@",kUsageOtherType_EveryTimesAmount, drugFormMd5, self.quickOrderTempId];
        NSString *otherTypeEstimateDays = [NSString stringWithFormat:@"%@_%@_%@", kUsageOtherType_EstimateDays, drugFormMd5, self.quickOrderTempId];
        
        
        [Utils saveUsageType:kandYTypeAll text:@"" withPatientId:self.patientId];
        [Utils saveUsageType:kandYTypeEveryDayAmount text:@"" withPatientId:self.patientId];
        [Utils saveUsageType:kandYTypeTimes text:@"" withPatientId:self.patientId];
        [Utils saveUsageType:kUsageMethodKey text:@"" withPatientId:self.patientId];
        
        [Utils saveUsageType:otherTypeEveryDayTimes text:@"" withPatientId:self.patientId];
        [Utils saveUsageType:otherTypeEveryTimesAmount text:@"" withPatientId:self.patientId];
        [Utils saveUsageType:otherTypeEstimateDays text:@"" withPatientId:self.patientId];
        
    }
    else{
        //先切换成本人
        [self setPatientInfoWithPatientIndex:0];
        
        
        //更新保存的用量数据
        NSString *drugFormMd5 = [[self.currentDrugForm md5String] substringToIndex:5];
        NSString *kandYTypeAll = [NSString stringWithFormat:@"%@",kUsageKandYType_All];
        NSString *kandYTypeEveryDayAmount = [NSString stringWithFormat:@"%@",kUsageKandYType_EveryDayAmount];
        NSString *kandYTypeTimes = [NSString stringWithFormat:@"%@",kUsageKandYType_times];
        
        
        NSString *otherTypeEveryDayTimes = [NSString stringWithFormat:@"%@_%@",kUsageOtherType_EveryDayTimes, drugFormMd5];
        NSString *otherTypeEveryTimesAmount = [NSString stringWithFormat:@"%@_%@",kUsageOtherType_EveryTimesAmount, drugFormMd5];
        NSString *otherTypeEstimateDays = [NSString stringWithFormat:@"%@_%@", kUsageOtherType_EstimateDays, drugFormMd5];
        
        
        [Utils saveUsageType:kandYTypeAll text:@"" withPatientId:self.patientId];
        [Utils saveUsageType:kandYTypeEveryDayAmount text:@"" withPatientId:self.patientId];
        [Utils saveUsageType:kandYTypeTimes text:@"" withPatientId:self.patientId];
        
        [Utils saveUsageType:otherTypeEveryDayTimes text:@"" withPatientId:self.patientId];
        [Utils saveUsageType:otherTypeEveryTimesAmount text:@"" withPatientId:self.patientId];
        [Utils saveUsageType:otherTypeEstimateDays text:@"" withPatientId:self.patientId];
        [Utils saveUsageType:kUsageMethodKey text:@"" withPatientId:self.patientId];
        
        
        //删除本地存储的临时处方
        NSString *primaryKey = [NSString stringWithFormat:@"%@%@",[UserManager shareInstance].getUserId,self.patientId];
        [[IMDataBaseManager shareInstance]deleteTemporaryPrescriptionWithPrimaryKey:primaryKey];
        _temPresModel = nil;
        
        //发送成功后记录下发送复诊单状态
        if (_otherView.swich.isOn) {
            [[NSUserDefaults standardUserDefaults]setObject:@"send" forKey:kUserDefaultIfSendFZD];
        }
        else {
            [[NSUserDefaults standardUserDefaults]setObject:@"notSend" forKey:kUserDefaultIfSendFZD];
        }
        
        [self returnToTheOriginalConditionWithType:BRReturnToTheOriginalConditionTypeDefault];
        
        _otherView.saveButton.selected = NO;
        _otherView.secretButton.selected = NO;
        _otherView.secretButton.layer.borderColor = [UIColor br_insideDivisionLineColor].CGColor;
        _offlineView.offlineButton.selected = NO;
        _offlineView.offlineButton.layer.borderColor = [UIColor br_divisionLineColor].CGColor;
        
        if ([self.delegate respondsToSelector:@selector(scrollToChatViewController)]) {
            [self.delegate scrollToChatViewController];
        }
    }
}

#pragma mark- 页面要还原成初始化的情况
- (void)returnToTheOriginalConditionWithType:(BRReturnToTheOriginalConditionType)type{
    
    //还原患者  不是快速开方
    if (self.prescribeType == BRPrescribeTypeNormal) {
        [self setPatientInfoWithPatientIndex:0];
    }
    
    if (_drugArray.count > 0) {
        [_drugArray removeAllObjects];
    }
    
    if (type == BRReturnToTheOriginalConditionTypeDefault) {
        //辨证还原
        _diagnosesView.diagnosesTextView.text = @"";
    }
    
    //还原用药
    [_displayView setDrugList:@[]];
    
    for (BRFactoryModel *model in self.factoryArr) {
        
        model.isSelected = NO;
        for (BRSubFactoryModel *subModel in model.list) {
            subModel.isSelected  = NO;
        }
        
    }
    
    _masterSelectedIndex = 0;
    _detailSelectedIndex = 0;
    
    //还原用法用量
    _usageView.drugForm = @"颗粒";
    [_usageView.changeTimeButton setTitle:@"饭后一小时" forState:UIControlStateNormal];
    _usageView.drugNumTextField.text = @"7";
    _usageView.usageTextField.text = @"1";
    _usageView.timesTextField.text = @"2";
    
    // 重置规格相关状态
    _usageView.packageSpecList = nil;
    _usageView.selectedSpecification = @"";
    _usageView.currentFactoryModel = nil;
    [_usageView updateSpecificationDisplayText:@"请选择规格"];
    [_usageView setSpecificationRowHidden:YES]; // 默认隐藏规格行
    
    //还原用法用量
    [self changeUsageHeightToNormalType]; // 这会处理正确的高度，包括用药方法的隐藏
    
    //还原其它
    _otherView.dayTextField.text = @"7";
    [_otherView setContraindicationStr:@""];
    _otherView.descTextView.text = @"";
    if (_otherView.saveButton.isSelected) {
        [_otherView setSaveButtonState];
    }
    if (_otherView.secretButton.isSelected) {
        _otherView.saveButton.selected = NO;
    }
    if (_otherView.secretDoseButton.isSelected) {
        _otherView.secretDoseButton.selected = NO;
    }
    
    //还原按语
    _noteView.textView.text = @"";
    
    //还原订单金额
    _chargeView.drugForm = @"颗粒";
    if (type == BRReturnToTheOriginalConditionTypeDefault) {
        _chargeView.chargeTextField.text = _defaultCharge?_defaultCharge:0;
        _changedCharge = _defaultCharge;
    }
    
    NSDecimalNumberHandler*roundingBehavior = [NSDecimalNumberHandler decimalNumberHandlerWithRoundingMode:NSRoundUp scale:2 raiseOnExactness:NO raiseOnOverflow:NO raiseOnUnderflow:NO raiseOnDivideByZero:YES];
    
    NSString *chargeStr = [_chargeView.chargeTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    if (chargeStr.length == 0) {
        chargeStr = @"0";
    }
    
    //诊费不清空，然后计算总药费
    NSDecimalNumber *chargeDecimal = [NSDecimalNumber decimalNumberWithString:chargeStr];
    chargeDecimal = [chargeDecimal decimalNumberByMultiplyingBy:[NSDecimalNumber decimalNumberWithString:@"1.064"] withBehavior:roundingBehavior];
    
    _chargeView.priceDetailLabel.text = @"¥ 0.00";
    _totalPriceDecimal = chargeDecimal;
    [self setTotalPriceWithTotalPriceDecimal:chargeDecimal];
    
    //还原基础药费与医技服务费
    _chargeView.serviceTextField.text = @"0.00";
    _drugPrice = @"0";
    _chargeView.drugPriceLabel.text = @"¥ 0.00";
    _makeCost = @"0";
    _balanceWeight = @"0";
    
    //还原线下诊断
    if (_offlineView.offlineButton.isSelected) {
        _offlineView.offlineButton.selected = NO;
        _offlineView.offlineButton.layer.borderColor = [UIColor br_divisionLineColor].CGColor;
    }
    
    //还原 金额 与 用法用量 的高度
    [self changeUsageHeightToNormalType];
    
}

#pragma mark 恢复未完成处方页面
- (void)restoredToTemporaryPrescriptionWithTemPresModel:(BRTemporaryPrescription *)temPresModel {
    
    NSString *prescriptionStr = temPresModel.prescriptionStr;
    NSDictionary *presDict = [prescriptionStr mj_JSONObject];
    
    //首先恢复患者信息
    if (self.prescribeType == BRPrescribeTypeQuick || self.prescribeType == BRPrescribeTypeMessage) {
        // 快速开方恢复患者信息到输入界面
        self.patientName = [presDict objectForKey:@"takerName"];
        self.patientAge = [presDict objectForKey:@"takerAge"];
        self.patientSex = [presDict objectForKey:@"takerSex"];
        self.patient_is_pregnanet = [presDict objectForKey:@"takerIsPregnant"];

        // 更新界面显示
        if (self.writePatientInfoView) {
            self.writePatientInfoView.nickname = self.patientName;
            self.writePatientInfoView.ageString = self.patientAge;
            self.writePatientInfoView.gender = [self.patientSex isEqualToString:@"1"] ? IMContactGenderMale : IMContactGenderFemale;
            self.writePatientInfoView.isPregnant = self.patient_is_pregnanet;
        }
    } else {
        // 正常开方恢复患者模型
        BRPatientModel *patientModel = [[BRPatientModel alloc]init];
        patientModel.age = [presDict objectForKey:@"takerAge"];
        patientModel.patientId = [presDict objectForKey:@"takerId"];
        patientModel.isPregnant = [presDict objectForKey:@"takerIsPregnant"];
        patientModel.sex = [presDict objectForKey:@"takerSex"];
        patientModel.name = [presDict objectForKey:@"takerName"];

        if ([patientModel.patientId isEqualToString:_patientId]) {
            patientModel.isSelf = @"1";
        }
        else {
            patientModel.isSelf = @"0";
        }
        [self setPatientInfoWithPatientModel:patientModel isSync:YES];
    }
    
    //恢复辨证信息
    _diagnosesView.diagnosesTextView.text = [presDict objectForKey:@"description"];
    
    /**
     * 恢复药房数据
     * 由于很多情况药房数据还未请求回来，所以如果没有药方数据要去请求
     */
    [self getPrescriptionFactoryIndex];
    
    NSString *drugForm = [presDict objectForKey:@"drugForm"];
    
    //恢复用法用量
    _usageView.drugForm = drugForm;
    
    // 恢复规格信息和辅料信息
    BRFactoryModel *factoryModel = [_factoryArr objectAtIndex:_masterSelectedIndex];
    NSArray *factoryArr = factoryModel.list;
    BRSubFactoryModel *subFactoryModel = [factoryArr objectAtIndex:_detailSelectedIndex];
    [_usageView updateSpecificationWithFactoryModel:subFactoryModel];
    [_usageView updateAuxiliaryMaterialWithFactoryModel:subFactoryModel];
    
    // 如果是代煎剂型，初始化代煎偏好数据
    if ([factoryModel.drugFormName isEqualToString:@"代煎"]) {
        [self initDecoctionPreferenceData];
        // 更新UI显示
        if (self.selectedDecoctionPreference.length > 0) {
            _otherView.decoctionPreference = self.selectedDecoctionPreference;
        }
        NSLog(@"恢复规格信息时 - 初始化代煎偏好数据: %@", self.selectedDecoctionPreference);
    }
    
    if ((![drugForm isEqualToString:@"颗粒"] &&
         ![drugForm isEqualToString:@"饮片"] &&
         ![drugForm isEqualToString:@"外用中药"]) ||
        ([drugForm isEqualToString:@"代煎"] && self.isUsePharmacyMake)) {
        
        [self changeUsageHeightToUnnormalType];
        /**
         * 如果不保存药材直接返回的话，这里显示不对
        _usageView.totalDoseStr = [NSString stringWithFormat:@"%@g(%@)",[presDict objectForKey:@"balanceWeight"],[presDict objectForKey:@"makeDescription"]];
         */
        _usageView.totalDoseStr = @"0g";
        _usageView.timesTextField.text = [presDict objectForKey:@"mrjc"];
        
        // 根据剂型和规格，从mcjk重量值恢复显示值
        NSString *mcjkValue = [presDict objectForKey:@"mcjk"];
        if ([drugForm isEqualToString:@"膏方"]) {
            if (_usageView.isCreamFormulaBottlePackage) {
                // 瓶装膏方：直接显示重量值
                _usageView.preDoseTextField.text = mcjkValue;
                _usageView.creamFormulaDirectWeightValue = [mcjkValue doubleValue];
            } else {
                // 非瓶装膏方：从重量值反推包装数量
                if (_usageView.currentCreamFormulaSpecGramPerUnit > 0) {
                    double packageCount = [mcjkValue doubleValue] / _usageView.currentCreamFormulaSpecGramPerUnit;
                    _usageView.selectedCreamFormulaUnitCount = (NSInteger)round(packageCount);
                    _usageView.preDoseTextField.text = [NSString stringWithFormat:@"%ld", (long)_usageView.selectedCreamFormulaUnitCount];
                } else {
                    _usageView.preDoseTextField.text = mcjkValue;
                }
            }
        } else if ([drugForm isEqualToString:@"蜜丸"]) {
            // 蜜丸：从重量值反推包装数量
            if (_usageView.currentSpecGramPerUnit > 0) {
                double packageCount = [mcjkValue doubleValue] / _usageView.currentSpecGramPerUnit;
                _usageView.selectedUnitCount = (NSInteger)round(packageCount);
                _usageView.preDoseTextField.text = [NSString stringWithFormat:@"%ld", (long)_usageView.selectedUnitCount];
            } else {
                _usageView.preDoseTextField.text = mcjkValue;
            }
        } else if ([drugForm isEqualToString:@"胶囊"]) {
            // 胶囊：从重量值反推包装数量
            if (_usageView.currentCapsuleSpecGramPerUnit > 0) {
                double packageCount = [mcjkValue doubleValue] / _usageView.currentCapsuleSpecGramPerUnit;
                _usageView.selectedCapsuleUnitCount = (NSInteger)round(packageCount);
                _usageView.preDoseTextField.text = [NSString stringWithFormat:@"%ld", (long)_usageView.selectedCapsuleUnitCount];
            } else {
                _usageView.preDoseTextField.text = mcjkValue;
            }
        } else if ([drugForm isEqualToString:@"水丸"]) {
            // 水丸：从重量值反推包装数量
            if (_usageView.currentWaterPillSpecGramPerUnit > 0) {
                double packageCount = [mcjkValue doubleValue] / _usageView.currentWaterPillSpecGramPerUnit;
                _usageView.selectedWaterPillUnitCount = (NSInteger)round(packageCount);
                _usageView.preDoseTextField.text = [NSString stringWithFormat:@"%ld", (long)_usageView.selectedWaterPillUnitCount];
            } else {
                _usageView.preDoseTextField.text = mcjkValue;
            }
        } else {
            // 散剂或其他剂型：直接显示重量值
            _usageView.preDoseTextField.text = mcjkValue;
        }
        
        // 恢复辅料信息（仅膏方剂型）
        if ([drugForm isEqualToString:@"膏方"]) {
            NSString *makeMaterial = [presDict objectForKey:@"makeMaterial"];
            if (makeMaterial && makeMaterial.length > 0) {
                if ([makeMaterial isEqualToString:@"不添加辅料"]) {
                    _usageView.isNoAuxiliaryMaterial = YES;
                    [_usageView.selectedAuxiliaryMaterials removeAllObjects];
                    [_usageView updateAuxiliaryMaterialDisplayText:@"不添加辅料"];
                } else {
                    _usageView.isNoAuxiliaryMaterial = NO;
                    NSArray *materials = [makeMaterial componentsSeparatedByString:@","];
                    [_usageView.selectedAuxiliaryMaterials removeAllObjects];
                    [_usageView.selectedAuxiliaryMaterials addObjectsFromArray:materials];
                    [_usageView updateAuxiliaryMaterialDisplayText:makeMaterial];
                }
            }
        }
        
        /**
         * 如果不保存药材直接返回的话，这里显示不对
        _usageView.dayTextField.text = [presDict objectForKey:@"takeDays"];
         */
        _usageView.dayTextField.text = @"1";
        
    }
    else {
        
        [self changeUsageHeightToNormalType];
        
        _usageView.drugNumTextField.text = [presDict objectForKey:@"totalPreNum"];
        _usageView.usageTextField.text = [presDict objectForKey:@"dayPreNum"];
        _usageView.timesTextField.text = [presDict objectForKey:@"preTimes"];
        
    }
    
    [_usageView.changeTimeButton setTitle:[presDict objectForKey:@"useTime"] forState:UIControlStateNormal];
    
    //恢复其它
    //发送复诊单
    _otherView.dayTextField.text = [presDict objectForKey:@"sendAfterDay"];
    [_otherView setContraindicationStr:[presDict objectForKey:@"contraindication"]];
    
    if ([[presDict objectForKey:@"isAutoSend"] isEqualToString:@"1"]) {
        [_otherView setSwichStateWithState:YES];
    }
    else {
        [_otherView setSwichStateWithState:NO];
    }
    
    
    NSString *isSaveTemplate = [presDict objectForKey:@"isSaveTemplate"];
    if ([isSaveTemplate isEqualToString:@"1"]) {
        [_otherView setSaveButtonState];
        _otherView.nameTextField.text = [presDict objectForKey:@"templateName"];
    }
    
    NSString *isSecrecy = [presDict objectForKey:@"isSecrecy"];
    if ([isSecrecy isEqualToString:@"1"]) {
        _otherView.secretButton.selected = YES;
        _otherView.secretButton.layer.borderColor = [UIColor br_mainBlueColor].CGColor;
    }
    if ([isSecrecy isEqualToString:@"2"]) {
        _otherView.secretDoseButton.selected = YES;
        _otherView.secretDoseButton.layer.borderColor = [UIColor br_mainBlueColor].CGColor;
    }
    
    //按语
    _noteView.textView.text = [presDict objectForKey:@"note"];
    
    //恢复金额
//    _chargeView.drugForm = drugForm;
    NSString *consultationfee = [presDict objectForKey:@"consultationfee"];
    if (consultationfee.length != 0) {
        _chargeView.chargeTextField.text = consultationfee;
        _changedCharge = consultationfee;
        
        NSDecimalNumberHandler*roundingBehavior = [NSDecimalNumberHandler decimalNumberHandlerWithRoundingMode:NSRoundUp scale:3 raiseOnExactness:NO raiseOnOverflow:NO raiseOnUnderflow:NO raiseOnDivideByZero:YES];
        
        //获取完诊费，计算总费用
        NSDecimalNumber *chargeDecimal = [NSDecimalNumber decimalNumberWithString:consultationfee];
        chargeDecimal = [chargeDecimal decimalNumberByMultiplyingBy:[NSDecimalNumber decimalNumberWithString:@"1.064"]];
        _totalPriceDecimal = [_totalPriceDecimal decimalNumberByAdding:chargeDecimal withBehavior:roundingBehavior];
        [self setTotalPriceWithTotalPriceDecimal:_totalPriceDecimal];
        
    }
    
    //线下
//    _offlineView.offlineButton.selected = [presDict objectForKey:@"offlineIsSelect"];
//    _offlineView.offlineButton.layer.borderColor = [UIColor br_mainBlueColor].CGColor;
    
    //如果有药材跳转添加药材页面，如果没有药材停留在药方页面
    NSArray *drugArr = [BRSubMedicineModel mj_objectArrayWithKeyValuesArray:[presDict objectForKey:@"preDetailList"]];
    [_drugArray addObjectsFromArray:drugArr];
    if (_drugArray.count > 0) {
        [self editSelectedDrugsWithType:YES];
    }
    
}

#pragma mark 找到临时处方用的剂型
- (void)getPrescriptionFactoryIndex {
    
    NSString *prescriptionStr = _temPresModel.prescriptionStr;
    NSDictionary *presDict = [prescriptionStr mj_JSONObject];
    
    NSString *drugForm = [presDict objectForKey:@"drugForm"];
    NSString *providerId = [presDict objectForKey:@"providerId"];
    NSString *productSubType = [presDict objectForKey:@"productSubType"];
    
    for (NSInteger index=0; index<_factoryArr.count; index++) {
        
        BRFactoryModel *factoryModel = [_factoryArr objectAtIndex:index];
        if ([drugForm isEqualToString:factoryModel.drugFormName]) {
            _masterSelectedIndex = index;
            break;
        }
        
    }
    
    BRFactoryModel *selectedFactoryModel = [_factoryArr objectAtIndex:_masterSelectedIndex];
    NSArray *subFactoryModelArr = selectedFactoryModel.list;
    
    for (NSInteger index=0; index<subFactoryModelArr.count; index++) {
        
        BRSubFactoryModel *subFactoryModel = [subFactoryModelArr objectAtIndex:index];
        if ([subFactoryModel.factoryId isEqualToString:providerId]) {
            _detailSelectedIndex = index;
            break;
        }
        
    }
    
}

#pragma mark 保存临时处方
- (void)saveTemporaryPrescription {

    BOOL isChanged = [self prescriptionIfChanged];
    if (!isChanged) {
        return;
    }

    if (_factoryArr.count == 0) {
        return;
    }

    // 对于快速开方，检查患者姓名是否存在
    if (self.prescribeType == BRPrescribeTypeQuick || self.prescribeType == BRPrescribeTypeMessage) {
        if (!self.patientName || self.patientName.length == 0) {
            return;
        }
    } else {
        // 正常开方检查患者模型
        if (!_patientModel) {
            return;
        }
    }
    
    NSString *diagnosesString = [_diagnosesView.diagnosesTextView.internalTextView.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    
    BRFactoryModel *factoryModel = [_factoryArr objectAtIndex:_masterSelectedIndex];
    NSArray *factoryArr = factoryModel.list;
    BRSubFactoryModel *subFactoryModel = [factoryArr objectAtIndex:_detailSelectedIndex];
    
    //药材总重
    NSString *balanceWeight = @"";
    if (_usageView.totalDoseLabel.text) {
        balanceWeight = [[_usageView.totalDoseLabel.text componentsSeparatedByString:@"g"] firstObject];
    }
    
    if (![factoryModel.drugFormName isEqualToString:@"颗粒"] &&
        ![factoryModel.drugFormName isEqualToString:@"饮片"] &&
        ![factoryModel.drugFormName isEqualToString:@"代煎"] &&
        ![factoryModel.drugFormName isEqualToString:@"外用中药"]) {
        
        //非颗粒与饮片部分
        //每日几次
        NSString *timesStr = [_usageView.timesTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        //每次几g
        NSString *preDoseStr = [_usageView.preDoseTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        //预计服用几天
        NSString *dayStr = [_usageView.dayTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        
    }
    else {
        
        //颗粒与饮片部分
        NSString *drugNumStr = [_usageView.drugNumTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        //每日几剂
        NSString *usageStr = [_usageView.usageTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        //每剂分几次服用
        NSString *timesStr = [_usageView.timesTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        
    }
    
    //复诊时间
    NSString *dayStr = [_otherView.dayTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    
    if (_otherView.saveButton.isSelected) {
        
        NSString *nameStr = [_otherView.nameTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        
    }
    
    //补充说明
    NSString *descStr = [_otherView.descTextView.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    if (descStr.length == 0) {
        descStr = @"";
    }
    
    //按语
    NSString *noteStr = [_noteView.textView.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    if (noteStr.length == 0) {
        noteStr = @"";
    }
    
    NSString *diagnosesStr = [_diagnosesView.diagnosesTextView.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    if (diagnosesStr.length == 0) {
        diagnosesStr = @"";
    }
    
    //共几付
    NSString *drugNum = @"";
    //每日几付
    NSString *dayPreNum = @"";
    //每付几次
    NSString *preTimes = @"";
    //每日几次
    NSString *mrjc = @"";
    //每次几克
    NSString *mcjk = @"";
    //预计可用几天
    NSString *takeDays = @"";
    //制作费
    NSString *makeCost = @"";
    if (![factoryModel.drugFormName isEqualToString:@"颗粒"] &&
         ![factoryModel.drugFormName isEqualToString:@"饮片"] &&
         ![factoryModel.drugFormName isEqualToString:@"代煎"] &&
         ![factoryModel.drugFormName isEqualToString:@"外用中药"]) {
        mrjc = _usageView.timesTextField.text;
        
        // mcjk字段应该存储重量值（克数），而非包装数量
        if ([factoryModel.drugFormName isEqualToString:@"膏方"]) {
            if (_usageView.isCreamFormulaBottlePackage) {
                // 瓶装膏方：直接使用输入的重量值
                mcjk = [NSString stringWithFormat:@"%.1f", _usageView.creamFormulaDirectWeightValue];
            } else {
                // 非瓶装膏方：计算总重量 = 包装数量 × 每单位重量
                double totalWeight = _usageView.selectedCreamFormulaUnitCount * _usageView.currentCreamFormulaSpecGramPerUnit;
                mcjk = [NSString stringWithFormat:@"%.1f", totalWeight];
            }
        } else if ([factoryModel.drugFormName isEqualToString:@"蜜丸"]) {
            // 蜜丸：计算总重量 = 包装数量 × 每单位重量
            double totalWeight = _usageView.selectedUnitCount * _usageView.currentSpecGramPerUnit;
            mcjk = [NSString stringWithFormat:@"%.1f", totalWeight];
        } else if ([factoryModel.drugFormName isEqualToString:@"胶囊"]) {
            // 胶囊：计算总重量 = 包装数量 × 每单位重量
            double totalWeight = _usageView.selectedCapsuleUnitCount * _usageView.currentCapsuleSpecGramPerUnit;
            mcjk = [NSString stringWithFormat:@"%.1f", totalWeight];
        } else if ([factoryModel.drugFormName isEqualToString:@"水丸"]) {
            // 水丸：计算总重量 = 包装数量 × 每单位重量
            double totalWeight = _usageView.selectedWaterPillUnitCount * _usageView.currentWaterPillSpecGramPerUnit;
            mcjk = [NSString stringWithFormat:@"%.1f", totalWeight];
        } else {
            // 散剂或其他剂型：直接使用输入框的重量值
            mcjk = _usageView.preDoseTextField.text;
        }
        
        takeDays = _usageView.dayTextField.text;
        if (_chargeView.productionCostsLabel.text != 0) {
            makeCost = [[_chargeView.productionCostsLabel.text stringByReplacingOccurrencesOfString:@"¥" withString:@""] stringByReplacingOccurrencesOfString:@" " withString:@""];
        }
    }
    else {
        drugNum = _usageView.drugNumTextField.text;
        dayPreNum = _usageView.usageTextField.text;
        preTimes = _usageView.timesTextField.text;
    }
    
    //诊费
    NSString *consultationfee = [_chargeView.chargeTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    if (consultationfee.length == 0) {
        consultationfee = @"0";
    }
    
    //服药禁忌
    NSString *contraindication = _otherView.contraindicationButton.titleLabel.text;
    if ([contraindication isEqualToString:@"选择服药禁忌"]) {
        contraindication = @"";
    }
    
    //是否保存处方
    NSString *isSaveTemplate = @"0";
    if (_otherView.saveButton.isSelected) {
        isSaveTemplate = @"1";
    }
    else {
        isSaveTemplate = @"0";
    }
    
    //是否保密
    NSString *isSecrecy = @"0";
    if (_otherView.secretButton.isSelected) {
        isSecrecy = @"1";
    }
    else if (_otherView.secretDoseButton.isSelected) {
        isSecrecy = @"2";
    }
    else {
        isSecrecy = @"0";
    }
    
    NSMutableArray *drugArr = [[NSMutableArray alloc]init];
    NSMutableArray *detailsArr = [[NSMutableArray alloc]init];
    for (NSInteger index=0; index<_drugArray.count; index++) {
        BRSubMedicineModel *model = [_drugArray objectAtIndex:index];
        
        NSString *useMethod = @"";
        if (model.useMethod) {
            useMethod = model.useMethod;
        }
        
        NSDictionary *dict = @{
                               @"dose":model.dose,
                               @"drugId":model.drugId,
                               @"drugName":model.drugName,
                               @"index":[NSString stringWithFormat:@"%ld",index],
                               @"unit":model.unit,
                               @"useMethod":useMethod
                               };
        NSDictionary *detailDict = @{
                                     @"amount":model.dose,
                                     @"name":model.drugName,
                                     @"type":useMethod,
                                     @"unit":model.unit
                                     };
        
        [drugArr addObject:dict];
        [detailsArr addObject:detailDict];
        
    }
    
    //多少天后发送问诊单
    NSString *sendAfterDay = [_otherView.dayTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    
    //模板名称
    NSString *templateName = @"";
    if (_otherView.saveButton.isSelected) {
        templateName = [_otherView.nameTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
    }
    
    NSString *costDesc = subFactoryModel.costDesc;
    
    if (!_makeDays) {
        _makeDays = @"";
    }
    
    NSString *offlineIsSelect = @"0";
    if (_offlineView.offlineButton.isSelected) {
        offlineIsSelect = @"1";
    }
    else {
        offlineIsSelect = @"0";
    }
    
    //是否发送复诊单
    NSString *isAutoSend = @"1";
    if (_otherView.swich.isOn) {
        isAutoSend = @"1";
    }
    else {
        isAutoSend = @"0";
    }
    
    // 根据开方类型设置患者信息
    NSString *takerAge, *takerId, *takerIsPregnant, *takerName, *takerSex, *patientId;

    if (self.prescribeType == BRPrescribeTypeQuick || self.prescribeType == BRPrescribeTypeMessage) {
        // 快速开方使用输入的患者信息
        takerAge = self.patientAge ?: @"";
        takerId = @""; // 快速开方没有患者ID
        takerIsPregnant = self.patient_is_pregnanet ?: @"0";
        takerName = self.patientName ?: @"";
        takerSex = self.patientSex ?: @"1";
        patientId = @""; // 快速开方没有患者ID
    } else {
        // 正常开方使用患者模型信息
        takerAge = _patientModel.age ?: @"";
        takerId = _patientModel.patientId ?: @"";
        takerIsPregnant = _patientModel.isPregnant ?: @"0";
        takerName = _patientModel.name ?: @"";
        takerSex = _patientModel.sex ?: @"1";
        patientId = _patientId ?: @"";
    }
    
    // 构建辅料信息
    NSString *auxiliaryMaterialInfo = @"";
    if ([factoryModel.drugFormName isEqualToString:@"膏方"]) {
        if (_usageView.isNoAuxiliaryMaterial) {
            auxiliaryMaterialInfo = @"不添加辅料";
        } else if (_usageView.selectedAuxiliaryMaterials.count > 0) {
            auxiliaryMaterialInfo = [_usageView.selectedAuxiliaryMaterials componentsJoinedByString:@","];
        }
    }
    
    NSMutableDictionary *orderDict = [NSMutableDictionary dictionaryWithDictionary:@{
                                @"balanceWeight":balanceWeight,
                                @"consultationfee":consultationfee,
                                @"contraindication":contraindication,
                                @"dayPreNum":dayPreNum,
                                @"description":diagnosesStr,
                                @"drugForm":factoryModel.drugFormName,
                                @"drugPrice":@"",
                                @"instructions":descStr,
                                @"isSaveTemplate":isSaveTemplate,
                                @"isSecrecy":isSecrecy,
                                @"makeDescription":costDesc,
                                @"mcjk":mcjk,
                                @"mrjc":mrjc,
                                @"note":noteStr,
                                @"patientId":patientId,
                                @"preDetailList":drugArr,
                                @"preTimes":preTimes,
                                @"productSubType":subFactoryModel.productSubType,
                                @"providerId":subFactoryModel.factoryId,
                                @"sendAfterDay":sendAfterDay,
                                @"takeDays":takeDays,
                                @"takerAge":takerAge,
                                @"takerId":takerId,
                                @"takerIsPregnant":takerIsPregnant,
                                @"takerName":takerName,
                                @"takerSex":takerSex,
                                @"templateName":templateName,
                                @"totalPreNum":drugNum,
                                @"useTime":_usageView.changeTimeButton.titleLabel.text,
                                @"makeCost":makeCost,
                                @"makeDays":_makeDays,
                                @"offlineIsSelect":offlineIsSelect,
                                @"isAutoSend":isAutoSend
                                }];
    
    // 添加辅料信息到字典中
    if (auxiliaryMaterialInfo.length > 0) {
        [orderDict setValue:auxiliaryMaterialInfo forKey:@"makeMaterial"];
    }
    
    
    NSString *prescriptionStr = [orderDict mj_JSONString];

    _temPresModel = [[BRTemporaryPrescription alloc]init];
    _temPresModel.prescriptionStr = prescriptionStr;

    // 根据开方类型设置主键和患者ID
    if (self.prescribeType == BRPrescribeTypeQuick || self.prescribeType == BRPrescribeTypeMessage) {
        // 快速开方使用患者姓名作为主键的一部分
        _primaryKey = [NSString stringWithFormat:@"quick_%@_%@", [UserManager shareInstance].getUserId, self.patientName];
        _temPresModel.primaryKey = _primaryKey;
        _temPresModel.patientId = self.patientName; // 快速开方使用患者姓名作为患者标识
    } else {
        // 正常开方使用原有逻辑
        _temPresModel.primaryKey = _primaryKey;
        _temPresModel.patientId = _patientId;
    }

    [[IMDataBaseManager shareInstance]saveTemporaryPrescription:_temPresModel];
    
    
}

#pragma mark-  delegate 更新带text view的view的高度
//更新辨证的高度
- (void)updateDiagnosesTextViewContainerViewHeight:(CGFloat)height {
    
    [self.view layoutIfNeeded];
    CGFloat oldDiagnosesHeight = _diagnosesView.height;
    CGFloat newDiagnosesHeight = kTitleViewHeight+10+10+20+5+height;
    
    [_diagnosesView mas_updateConstraints:^(MASConstraintMaker *make) {
       
        make.height.mas_equalTo(newDiagnosesHeight);
        
    }];
    
    _scrollView.contentSize = CGSizeMake(kScreenWidth, _scrollView.contentSize.height-oldDiagnosesHeight+newDiagnosesHeight);
    
}
//更新其它的高度
- (void)updateDescTextViewContainerViewHeight:(CGFloat)containerHeight contraindicationHeight:(CGFloat)contraindicationHeight{
    
//    [self.view layoutIfNeeded];
//    CGFloat oldOtherViewHeight = _otherView.height;
//    CGFloat newOtherViewHeight = 0.0f;
//    if (_otherView.saveButton.isSelected) {
//        newOtherViewHeight = kTitleViewHeight+50+15+contraindicationHeight+15+15+containerHeight+15+50+50;
//    }
//    else {
//        newOtherViewHeight = kTitleViewHeight+50+15+contraindicationHeight+15+15+containerHeight+15+50+50;
//    }
    
//    [_otherView mas_updateConstraints:^(MASConstraintMaker *make) {
//
//        make.height.mas_equalTo(newOtherViewHeight);
//
//    }];
    
    
//    _scrollView.contentSize = CGSizeMake(kScreenWidth, _scrollView.contentSize.height-oldOtherViewHeight+newOtherViewHeight);
    
}

//点击保存药方名的点击事件 更新other view的高度
- (void)saveButtonClick:(BOOL)buttonState {
    
    [self.view layoutIfNeeded];
    
    CGFloat oldOtherHeight = _otherView.height;
    CGFloat oldContentHeight = _scrollView.contentSize.height;
    if (buttonState) {
        [_otherView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(oldOtherHeight+50);
        }];
        _scrollView.contentSize = CGSizeMake(kScreenWidth, oldContentHeight+50);
    }
    else {
        [_otherView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(oldOtherHeight-50);
        }];
        _scrollView.contentSize = CGSizeMake(kScreenWidth, oldContentHeight-50);
    }
    
}
//更新按语的高度
- (void)updateNoteTextViewContainerViewHeight:(CGFloat)height {
    
    [self.view layoutIfNeeded];
    CGFloat oldNoteViewHeight = _noteView.height;
    CGFloat newNoteViewHeight = kTitleViewHeight+10+10+20+5+height;
    
    [_noteView mas_updateConstraints:^(MASConstraintMaker *make) {
        
        make.height.mas_equalTo(newNoteViewHeight);
        
    }];
    
    _scrollView.contentSize = CGSizeMake(kScreenWidth, _scrollView.contentSize.height-oldNoteViewHeight+newNoteViewHeight);
    
}

- (void)brPresOtherViewDelegateUpdateMakeSwitchValue:(BOOL)value {
    
//    _isUsePharmacyMake = value;
    
    //更新明细显示
//    self.chargeView.isUsePharmacyMake = value;
    self.chargeView.isUsePharmacyMake = YES;
    
    if (self.currentDrugForm.length > 0) {
        [self.chargeView setDrugForm:self.currentDrugForm];
    }
    
    [self calculateDrugCostsWithServiceProportion:_serviceProportion type:BRCalculateChargeModifyALL];
}

#pragma mark- 添加药材代理
- (void)addDrugListToPrescriptionWithDrugList:(NSArray *)drugList detailSelectedIndex:(NSInteger)detailSelectedIndex masterSelectedIndex:(NSInteger)masterSelectedIndex balanceWeight:(NSString *)balanceWeight drugPrice:(NSString *)drugPrice makeCost:(NSString *)makeCost makeDays:(NSString *)makeDays makeDesc:(NSString *)makeDesc weight:(CGFloat)weight{
    
    _detailSelectedIndex = detailSelectedIndex;
    _masterSelectedIndex = masterSelectedIndex;
    
    _drugPrice = drugPrice;
    _makeDays = makeDays;
    _makeCost = makeCost;
    _balanceWeight = balanceWeight;
    
    BRFactoryModel *factoryModel = [_factoryArr objectAtIndex:masterSelectedIndex];
    NSArray *factoryArr = factoryModel.list;
    BRSubFactoryModel *subFactoryModel = [factoryArr objectAtIndex:detailSelectedIndex];
    
    _currentDrugForm = factoryModel.drugFormName;
    
    _displayView.factoryStr = [NSString stringWithFormat:@"%@(%@)",factoryModel.drugFormName,subFactoryModel.factoryName];
    
    [_displayView setDrugList:drugList];
    if (_drugArray.count > 0) {
        [_drugArray removeAllObjects];
        [_drugArray addObjectsFromArray:drugList];
    }
    else {
        [_drugArray addObjectsFromArray:drugList];
    }
    
    // 先设置剂型，再更新规格信息
    _usageView.drugForm = factoryModel.drugFormName;
    _chargeView.drugForm = factoryModel.drugFormName;
    
    // 更新用法用量界面的规格信息和辅料信息（此时drugForm已经是最新的）
    [_usageView updateSpecificationWithFactoryModel:subFactoryModel];
    [_usageView updateAuxiliaryMaterialWithFactoryModel:subFactoryModel];
    
    // 如果是代煎剂型，初始化代煎偏好数据
    if ([factoryModel.drugFormName isEqualToString:@"代煎"]) {
        [self initDecoctionPreferenceData];
        // 更新UI显示
        if (self.selectedDecoctionPreference.length > 0) {
            _otherView.decoctionPreference = self.selectedDecoctionPreference;
        }
        NSLog(@"更新规格信息时 - 初始化代煎偏好数据: %@", self.selectedDecoctionPreference);
    }
    
    NSNumberFormatter *numberFormatter = [[NSNumberFormatter alloc]init];
    [numberFormatter setPositiveFormat:@"0.00"];
    
    if ([factoryModel.drugFormName isEqualToString:@"代煎"]) {
        self.otherView.isShowSwitchView = YES;
        
        // 检查当前"每剂分几次服用"的值，如果大于4，则限制为4次
        NSString *timesStr = [_usageView.timesTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        int timesValue = [timesStr intValue];
        
        if (timesValue > 4) {
            // 修改为4，并显示提示信息
            _usageView.timesTextField.text = @"2";
            
            // 保存选择的次数
            NSString *type = [NSString stringWithFormat:@"%@", kUsageKandYType_times];
            // 如果为快速开方
            if(self.quickOrderTempId.length > 0) {
                type = [NSString stringWithFormat:@"%@_%@", type, self.quickOrderTempId];
            }
            [Utils saveUsageType:type text:@"2" withPatientId:self.patientId];
            
            // 显示提示信息
            // [self.view makeToast:@"代煎剂型每剂最多分4次服用" duration:kToastDuration position:CSToastPositionCenter];
        }
    }
    else {
        self.otherView.isShowSwitchView = NO;
//        self.isUsePharmacyMake = NO;
    }
    
    if (![factoryModel.drugFormName isEqualToString:@"颗粒"] &&
        ![factoryModel.drugFormName isEqualToString:@"饮片"] &&
        ![factoryModel.drugFormName isEqualToString:@"代煎"] &&
        ![factoryModel.drugFormName isEqualToString:@"外用中药"]) {
        
        [self changeUsageHeightToUnnormalType];
        
        _usageView.totalDoseStr = [NSString stringWithFormat:@"%@g (原重%.1fg,%@)",balanceWeight,weight,makeDesc];
        
        if (balanceWeight.length != 0) {
            NSString *doseStr = [[balanceWeight componentsSeparatedByString:@"g"] firstObject];
            
            if ([doseStr floatValue] == 0) {
                
            }
            else {
                
                NSString *timesNum = [_usageView.timesTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
                NSString *preDoseNum = [_usageView.preDoseTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
                if ([timesNum intValue] == 0 || [preDoseNum integerValue] == 0 || timesNum.length == 0 || preDoseNum.length == 0) {
                    
                }
                else {
                    
                    CGFloat totalDose = [doseStr integerValue];
                    NSInteger day = totalDose/([timesNum integerValue]*[preDoseNum floatValue]);
                    _usageView.dayTextField.text = [NSString stringWithFormat:@"%ld",(long)day == 0?1:(long)day];
                    _otherView.dayTextField.text = [NSString stringWithFormat:@"%ld",((long)day == 0?1:(long)day) + [makeDays intValue]];
                }
                
            }
        }
    }
    else {
        
        //颗粒与饮片的显示
        [self changeUsageHeightToNormalType];
        
        NSString *drugNum = [_usageView.drugNumTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        
        if (drugNum.length == 0 || [drugNum integerValue] == 0) {
            
        }
        else {
            
            NSString *usageNum = [_usageView.usageTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
            
            if (usageNum.length == 0 || [usageNum integerValue] == 0) {
                _otherView.dayTextField.text = drugNum;
            }
            else {
                int times = [drugNum intValue]/[usageNum intValue];
                _otherView.dayTextField.text = [NSString stringWithFormat:@"%d",times];
            }
            
        }
        
    }
    
    //用法用量中显示胶囊的颗粒数
    [self showCapsuleParticlesNums];
    
    [self calculateDrugCostsWithServiceProportion:_serviceProportion type:BRCalculateChargeModifyALL];
    
}

- (void)cleanSelectedDrugs {
    
    if (_drugArray.count > 0) {
        [_drugArray removeAllObjects];
    }
    
    [self returnToTheOriginalConditionWithType:BRReturnToTheOriginalConditionTypeClearDrugs];
    
}

#pragma mark 计算药费
- (void)calculateDrugCostsWithServiceProportion:(NSString *)serviceProportion type:(BRCalculateCharge)type{
    
    NSDecimalNumberHandler*roundingBehavior = [NSDecimalNumberHandler decimalNumberHandlerWithRoundingMode:NSRoundUp scale:2 raiseOnExactness:NO raiseOnOverflow:NO raiseOnUnderflow:NO raiseOnDivideByZero:YES];
    
    NSNumberFormatter *numberFormatter = [[NSNumberFormatter alloc]init];
    [numberFormatter setPositiveFormat:@"0.00"];
    
    //单纯药价
    NSDecimalNumber *drugPriceDecimal = [NSDecimalNumber decimalNumberWithString:_drugPrice];
    //制作费
    NSDecimalNumber *makeCostDecimal = [NSDecimalNumber decimalNumberWithString:@"0"];
    
    NSString *chargeStr = @"0";
    if (_chargeView.chargeTextField.text.length != 0) {
        chargeStr = _chargeView.chargeTextField.text;
    }
    
    NSDecimalNumber *chargeDecimal = [NSDecimalNumber decimalNumberWithString:chargeStr];
    //诊费
    NSDecimalNumber *totalChargeDecimal = [chargeDecimal decimalNumberByMultiplyingBy:[NSDecimalNumber decimalNumberWithString:@"1.064"]];
    
    BRFactoryModel *factoryModel = [_factoryArr objectAtIndex:_masterSelectedIndex];
    NSArray *factoryArr = factoryModel.list;
    BRSubFactoryModel *subFactoryModel = [factoryArr objectAtIndex:_detailSelectedIndex];
    // 修改计算逻辑
    if (![factoryModel.drugFormName isEqualToString:@"颗粒"] &&
        ![factoryModel.drugFormName isEqualToString:@"外用中药"] &&
        ![factoryModel.drugFormName isEqualToString:@"饮片"] &&
        ![factoryModel.drugFormName isEqualToString:@"代煎"]) {
        
        if ([[[UserManager shareInstance] getApp_ShowMedicalServiceFeeRule] isEqualToString:@"0"]) {
            NSDecimalNumber *drugDecimal = [NSDecimalNumber decimalNumberWithString:_drugPrice];
            _chargeView.priceDetailLabel.text = [NSString stringWithFormat:@"¥ %@",drugDecimal];
        } else {
            _chargeView.priceDetailLabel.text = [NSString stringWithFormat:@"¥ %@",_drugPrice];
        }
        
        // 制作费
        _chargeView.productionCostsLabel.text = [NSString stringWithFormat:@"¥ %@",_makeCost];
        makeCostDecimal = [NSDecimalNumber decimalNumberWithString:_makeCost];
        
    } else {
        // 验证剂数输入，防止无效字符串导致异常
        NSString *drugNumText = _usageView.drugNumTextField.text;
        NSString *validDrugNumText = (drugNumText && drugNumText.length > 0) ? drugNumText : @"1";
        NSDecimalNumber *drugNumDecimal = [NSDecimalNumber decimalNumberWithString:validDrugNumText];
        NSDecimalNumber *totalDrugPriceDecimal = [drugPriceDecimal decimalNumberByMultiplyingBy:drugNumDecimal];
        
        NSDecimalNumberHandler *roundingBehavior3 = [NSDecimalNumberHandler decimalNumberHandlerWithRoundingMode:NSRoundUp scale:3 raiseOnExactness:NO raiseOnOverflow:NO raiseOnUnderflow:NO raiseOnDivideByZero:YES];
        NSDecimalNumber *drugDecimal = [NSDecimalNumber decimalNumberWithString:_drugPrice];
        
        _chargeView.priceDetailLabel.text = [NSString stringWithFormat:@"¥ %@ * %ld = %@",drugDecimal,[_usageView.drugNumTextField.text integerValue], [numberFormatter stringFromNumber:[totalDrugPriceDecimal decimalNumberByRoundingAccordingToBehavior:roundingBehavior]]];
        
        drugPriceDecimal = [NSDecimalNumber decimalNumberWithString:[NSString stringWithFormat:@"%@",totalDrugPriceDecimal]];
        
        // 增加判断是否需要计算制作费
        if (([factoryModel.drugFormName isEqualToString:@"代煎"] && self.isUsePharmacyMake) ||
            (([factoryModel.drugFormName isEqualToString:@"颗粒"] ||
              [factoryModel.drugFormName isEqualToString:@"外用中药"]) &&
             _makeCost.length > 0 && ![_makeCost isEqualToString:@"0"])) {
            
            // 验证剂数输入，防止无效字符串导致异常
            NSString *drugNumText = _usageView.drugNumTextField.text;
            NSString *validDrugNumText = (drugNumText && drugNumText.length > 0) ? drugNumText : @"1";
            NSDecimalNumber *drugNumDecimal = [NSDecimalNumber decimalNumberWithString:validDrugNumText];
            NSDecimalNumber *singleMakeCost = [NSDecimalNumber decimalNumberWithString:_makeCost];
            NSDecimalNumber *totalDrugMakeCostDecimal = [singleMakeCost decimalNumberByMultiplyingBy:drugNumDecimal];
            _chargeView.productionCostsLabel.text = [NSString stringWithFormat:@"¥ %.3f * %ld = %@",[_makeCost floatValue],[_usageView.drugNumTextField.text integerValue],[numberFormatter stringFromNumber:[totalDrugMakeCostDecimal decimalNumberByRoundingAccordingToBehavior:roundingBehavior]]];
            makeCostDecimal = totalDrugMakeCostDecimal;
        }
    }
    
    
    /**
     * 单纯药价X 医生默认提成比例 A
     * 医技服务费为 AX/(1-A）
     * 特殊剂型制作费为 Z
     * 药费为 X+AX/(1-A)+Z
     * 诊费为Y
     * 订单总金额 X+AX/(1-A)+Z+Y*1.064+（AX/(1-A)-X/0.7*0.3)*0.06*1.12+（AX/(1-A)-X/0.7*0.3)*0.01
     */
    
    //提成比例
    NSDecimalNumber *proportionDecimal = [NSDecimalNumber decimalNumberWithString:serviceProportion];
    
    // 防止除零溢出：当比例接近1时，分母接近0会导致溢出
    NSDecimalNumber *denominator = [[NSDecimalNumber decimalNumberWithString:@"1"] decimalNumberBySubtracting:proportionDecimal];
    NSDecimalNumber *serviceChargeDecimal;
    
    // 如果分母过小（接近0），则设置一个安全的默认值
    if ([denominator compare:[NSDecimalNumber decimalNumberWithString:@"0.001"]] == NSOrderedAscending) {
        // 当比例过高时，使用一个合理的上限值
        serviceChargeDecimal = [drugPriceDecimal decimalNumberByMultiplyingBy:[NSDecimalNumber decimalNumberWithString:@"999"]];
    } else {
        //计算医技服务费
        serviceChargeDecimal = [[drugPriceDecimal decimalNumberByMultiplyingBy:proportionDecimal] decimalNumberByDividingBy:denominator];
    }
    
    if (type == BRCalculateChargeModifyALL) {
        _chargeView.serviceTextField.text = [NSString stringWithFormat:@"%@",[numberFormatter stringFromNumber:[serviceChargeDecimal decimalNumberByRoundingAccordingToBehavior:roundingBehavior]]];
    }
    else {
        //不改变药费那么医技服务费为输入框中的
        NSString *serviceCharge = [_chargeView.serviceTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        if (serviceCharge.length == 0) {
            serviceCharge = @"0";
        }
        serviceChargeDecimal = [NSDecimalNumber decimalNumberWithString:serviceCharge];
    }
    //综合药费
    NSDecimalNumber *comDrugPriceDecimal = [drugPriceDecimal decimalNumberByAdding:makeCostDecimal];
    _chargeView.drugPriceLabel.text = [NSString stringWithFormat:@"¥ %@",[comDrugPriceDecimal decimalNumberByRoundingAccordingToBehavior:roundingBehavior]];
    //增值税
    NSDecimalNumber *value_addedTax = [NSDecimalNumber decimalNumberWithString:@"0"];
    //手续费
    NSDecimalNumber *feesDecimal = [NSDecimalNumber decimalNumberWithString:@"0"];
    //总药价
    _totalPriceDecimal = [[[[drugPriceDecimal decimalNumberByAdding:totalChargeDecimal] decimalNumberByAdding:value_addedTax] decimalNumberByAdding:feesDecimal] decimalNumberByAdding:makeCostDecimal withBehavior:roundingBehavior];
    
    //总药价
    [self setTotalPriceWithTotalPriceDecimal:_totalPriceDecimal];
}

#pragma mark 给总药价赋值
- (void)setTotalPriceWithTotalPriceDecimal:(NSDecimalNumber *)totalPriceDecimal {
    
    if ([totalPriceDecimal floatValue] < 0) {
        totalPriceDecimal = [NSDecimalNumber decimalNumberWithString:@"0"];
    }
    
    NSNumberFormatter *numberFormatter = [[NSNumberFormatter alloc]init];
    [numberFormatter setPositiveFormat:@"0.00"];
    
    NSMutableAttributedString *totalStr = [[NSMutableAttributedString alloc]initWithString:[NSString stringWithFormat:@"¥ %@",[numberFormatter stringFromNumber:totalPriceDecimal]]];
    [totalStr addAttribute:NSFontAttributeName value:kFontLight(22) range:NSMakeRange(1, totalStr.length-1)];
    [totalStr addAttribute:NSForegroundColorAttributeName value:[UIColor br_textBlackColor] range:NSMakeRange(1, totalStr.length-1)];
    _chargeView.totalPriceLabel.attributedText = totalStr;
    
}

#pragma mark 改变用法用量为饮片、颗粒、外用中药
//改变成膏方、水丸、蜜丸、散剂等特殊剂型
- (void)changeUsageHeightToUnnormalType {
    
    [self.view layoutIfNeeded];
    
    BOOL shouldShowUsageMethod = [_usageView.drugForm isEqualToString:@"饮片"] ||
                                    [_usageView.drugForm isEqualToString:@"代煎"] ||
                                    [_usageView.drugForm isEqualToString:@"散剂"];
    CGFloat additionalHeight = shouldShowUsageMethod ? 50 : 0;
    
    CGFloat usageDiff = (kTitleViewHeight + 50 + 50 + 50 + additionalHeight) - _usageView.height;
    
    //特殊剂型的显示
    [_usageView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(kTitleViewHeight + 50 + 50 + 50 + additionalHeight);
    }];
    
    
//    CGFloat usageDiff = (kTitleViewHeight+50+50+50)-_usageView.height;
//    //特殊剂型的显示
//    [_usageView mas_updateConstraints:^(MASConstraintMaker *make) {
//        make.height.mas_equalTo(kTitleViewHeight+50+50+50);
//    }];
    
    BRFactoryModel *factoryModel = [_factoryArr objectAtIndex:_masterSelectedIndex];
    NSArray *factoryArr = factoryModel.list;
    BRSubFactoryModel *subFactoryModel = [factoryArr objectAtIndex:_detailSelectedIndex];
    CGFloat newChargeHeight = 0.0f;
//    if (![factoryModel.drugFormName isEqualToString:@"颗粒"] &&
//        ![factoryModel.drugFormName isEqualToString:@"饮片"] &&
//        ![factoryModel.drugFormName isEqualToString:@"代煎"] &&
//        ![factoryModel.drugFormName isEqualToString:@"外用中药"]) {
//        
//        if (_isShowDetail) {
//            newChargeHeight = kTitleViewHeight+50+250/2+50+50;
//        }
//        else {
//            newChargeHeight = kTitleViewHeight+50+50+50;
//        }
//        
//    }
//    else {
//        
//        if (_isShowDetail) {
//            newChargeHeight = kTitleViewHeight+50+180/2+50+50;
//        }
//        else {
//            newChargeHeight = kTitleViewHeight+50+50+50;
//        }
//        
//    }
    //颗粒、外用中药  和  代煎保持一致
    if (![factoryModel.drugFormName isEqualToString:@"饮片"]) {
            
        if (_isShowDetail) {
            newChargeHeight = kTitleViewHeight+50+250/2+50+50;
        }
        else {
            newChargeHeight = kTitleViewHeight+50+50+50;
        }
        
    }
    else {
        // 现在这个分支处理:饮片、颗粒、代煎、外用中药
        if (_isShowDetail) {
            newChargeHeight = kTitleViewHeight+50+180/2+50+50;
        }
        else {
            newChargeHeight = kTitleViewHeight+50+50+50;
        }
        
    }
    
    CGFloat chargeDiff = newChargeHeight-_chargeView.height;
    
    [_chargeView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(newChargeHeight);
    }];
    
    CGFloat oldContentHeight = _scrollView.contentSize.height;
    
    _scrollView.contentSize = CGSizeMake(kScreenWidth, oldContentHeight+usageDiff+chargeDiff);
    
//    CGFloat oldContentHeight = _scrollView.contentSize.height;
//    _scrollView.contentSize = CGSizeMake(kScreenWidth, oldContentHeight + usageDiff + chargeDiff);
    
}
//改变为饮片、颗粒及外用中药的type
- (void)changeUsageHeightToNormalType {
    [self.view layoutIfNeeded];

    // 计算 charge view 的高度
    CGFloat newChargeHeight = 0.0f;
    if (_isShowDetail) {
        newChargeHeight = kTitleViewHeight + 50 + 180/2 + 50 + 50;
    } else {
        newChargeHeight = kTitleViewHeight + 50 + 50 + 50;
    }
    
    // 判断是否需要显示用药方法
    BOOL shouldShowUsageMethod = [_usageView.drugForm isEqualToString:@"饮片"] ||
                                [_usageView.drugForm isEqualToString:@"代煎"] ||
                                [_usageView.drugForm isEqualToString:@"散剂"];
    CGFloat additionalHeight = shouldShowUsageMethod ? 50 : 0;
    
    // 计算高度差
    CGFloat usageDiff = (kTitleViewHeight + 50 + 50 + additionalHeight) - _usageView.height;
    CGFloat chargeDiff = newChargeHeight - _chargeView.height;
    
    // 更新用法用量视图高度
    [_usageView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(kTitleViewHeight + 50 + 50 + additionalHeight);
    }];
    
    // 更新金额视图高度
    [_chargeView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(newChargeHeight);
    }];
    
    // 更新 scrollView 的内容高度
    CGFloat oldContentHeight = _scrollView.contentSize.height;
    _scrollView.contentSize = CGSizeMake(kScreenWidth, oldContentHeight + usageDiff + chargeDiff);
}

- (void)popViewControllerClearDrug {
    /*
    [self returnToTheOriginalConditionWithType:BRReturnToTheOriginalConditionTypeClearDrugs];
     */
    
    //删掉本地缓存
    NSString *primaryKey = [NSString stringWithFormat:@"%@%@",[UserManager shareInstance].getUserId,self.patientId];
    [[IMDataBaseManager shareInstance]deleteTemporaryPrescriptionWithPrimaryKey:primaryKey];
    _temPresModel = nil;
    
    /*
        添加这个判断主要是为了不把  编辑药材  的  不保存返回的情况  给剔除。
        如果都走清空的这种情况会把 编辑药材 不保存返回的情况 也把原先添加的药材给清除掉
     */
    if (_drugArray.count > 0) {
        return;
    }
    
    [self returnToTheOriginalConditionWithType:BRReturnToTheOriginalConditionTypeDefault];
    
}

#pragma mark 判断是不是已经修改了用药界面
- (BOOL)prescriptionIfChanged {

    // 检查快速开方的患者信息是否有变化
    if (self.prescribeType == BRPrescribeTypeQuick || self.prescribeType == BRPrescribeTypeMessage) {
        if (self.patientName && self.patientName.length > 0) {
            return YES;
        }
        if (self.patientAge && ![self.patientAge isEqualToString:@""]) {
            return YES;
        }
        if (self.patientSex && ![self.patientSex isEqualToString:@"1"]) {
            return YES;
        }
        if (self.patient_is_pregnanet && ![self.patient_is_pregnanet isEqualToString:@"0"]) {
            return YES;
        }
    }

    if (_diagnosesView.diagnosesTextView.text.length != 0) {
        return YES;
    }

    if (_drugArray.count > 0) {
        return YES;
    }
    
    if (![_usageView.drugForm isEqualToString:@"颗粒"] &&
        ![_usageView.drugForm isEqualToString:@"饮片"] &&
        ![_usageView.drugForm isEqualToString:@"代煎"] &&
        ![_usageView.drugForm isEqualToString:@"外用中药"] && _usageView.drugForm) {
        return YES;
    }
    else {
        
        if (![_usageView.drugNumTextField.text isEqualToString:@"7"]) {
            return YES;
        }
        if (![_usageView.usageTextField.text isEqualToString:@"1"]) {
            return YES;
        }
        if (![_usageView.timesTextField.text isEqualToString:@"2"]) {
            return YES;
        }
        if (![_usageView.changeTimeButton.titleLabel.text isEqualToString:@"饭后一小时"]) {
            return YES;
        }
        
    }
    
    NSString *swichState = @"send";
    if (_otherView.swich.isOn) {
        swichState = @"send";
    }
    else {
        swichState = @"notSend";
    }
    
    NSString *sendState = [[NSUserDefaults standardUserDefaults]objectForKey:kUserDefaultIfSendFZD];
    if (!sendState) {
        sendState = @"send";
    }
    if (![swichState isEqualToString:sendState]) {
        return YES;
    }
    
    
    if (![_otherView.dayTextField.text isEqualToString:@"7"]) {
        return YES;
    }
    
    if (![_otherView.contraindicationButton.titleLabel.text isEqualToString:@"选择服药禁忌"]) {
        return YES;
    }
    
    if (_otherView.descTextView.text.length != 0) {
        return YES;
    }
    
    if (_otherView.saveButton.isSelected) {
        return YES;
    }
    
    if (_otherView.secretButton.isSelected) {
        return YES;
    }
    
    if (_otherView.secretDoseButton.isSelected) {
        return YES;
    }
    
    if (_noteView.textView.text.length != 0) {
        return YES;
    }
    
    if (![_chargeView.chargeTextField.text isEqualToString:_defaultCharge]) {
        return YES;
    }
    
    if (_offlineView.offlineButton.isSelected) {
        return YES;
    }
    
    return NO;
    
}
- (void)endInputing {
    [self.view endEditing:YES];
}
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}

#pragma mark - private
- (void)dealloc {
    
    // 在 viewDidLoad 或其他初始化方法中
    [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(updateUserInfo:)
                                               name:kIMNotificationUPdatePatientInfo
                                             object:nil];

    // 在 dealloc 中
    [[NSNotificationCenter defaultCenter] removeObserver:self
                                                  name:kIMNotificationUPdatePatientInfo
                                                object:nil];
}

#pragma mark 添加显示服用次数选择的ActionSheet方法
- (void)showServingTimesActionSheet {
    NSLog(@"显示服用次数选择ActionSheet");
    NSArray *buttonsArr = @[@"1 次", @"2 次", @"3 次", @"4 次"];
    
    [self.view endEditing:YES];
    
    BRActionSheetView *actionSheet = [[BRActionSheetView alloc] init];
    actionSheet.title = @"选择次数";
    actionSheet.buttons = buttonsArr;
    [actionSheet show];
    
    __weak PrescriptionViewController *weakSelf = self;
    __weak BRPresUsageView *weakUsageView = _usageView;
    __weak BRActionSheetView *weakActionSheet = actionSheet;
    
    actionSheet.clickActionButtonCallBack = ^(NSInteger index) {
        NSLog(@"选择了服用次数: %@", [buttonsArr objectAtIndex:index]);
        [weakActionSheet close];
        
        // 解析选中的次数
        NSString *timesString = [buttonsArr objectAtIndex:index];
        NSString *timesValue = [[timesString componentsSeparatedByString:@" "] firstObject];
        
        // 更新输入框的值
        weakUsageView.timesTextField.text = timesValue;
        
        // 保存选择的次数
        NSString *type = [NSString stringWithFormat:@"%@", kUsageKandYType_times];
        // 如果为快速开方
        if(weakSelf.quickOrderTempId.length > 0) {
            type = [NSString stringWithFormat:@"%@_%@", type, weakSelf.quickOrderTempId];
        }
        [Utils saveUsageType:type text:timesValue withPatientId:weakSelf.patientId];
    };
    
    // 设置ActionSheet关闭回调
    actionSheet.actionSheetViewClosed = ^{
        NSLog(@"服用次数选择ActionSheet已关闭");
    };
}

#pragma mark - 获取当前的厂商
- (BRFactoryModel *)getCurrentFactoryModel {
    if (_factoryArr.count == 0 || _masterSelectedIndex >= _factoryArr.count) {
        return nil;
    }
    return [_factoryArr objectAtIndex:_masterSelectedIndex];
}

//- (BRSubFactoryModel *)getCurrentSubFactoryModel {
//    BRFactoryModel *factoryModel = [_factoryArr objectAtIndex:_masterSelectedIndex];
//    NSArray *factoryArr = factoryModel.list;
//    BRSubFactoryModel *subFactoryModel = [factoryArr objectAtIndex:_detailSelectedIndex];
//    return subFactoryModel;
//}

#pragma mark - 显示规格选择弹窗
- (void)showSpecificationActionSheet {
    // 获取当前厂商的规格列表
    BRSubFactoryModel *currentSubFactory = [self getCurrentSubFactoryModel];
    if (!currentSubFactory || !currentSubFactory.packageSpecList || currentSubFactory.packageSpecList.count == 0) {
        return;
    }
    
    // 构建选项数组
    NSMutableArray *specificationArray = [NSMutableArray array];
    for (BRPackageSpecModel *packageSpec in currentSubFactory.packageSpecList) {
        if (packageSpec.value && packageSpec.value.length > 0) {
            [specificationArray addObject:packageSpec.value];
        }
    }
    
    if (specificationArray.count == 0) {
        return;
    }
    
    [self.view endEditing:YES];
    
    _specificationActionSheet = [[BRActionSheetView alloc] init];
    _specificationActionSheet.title = @"选择规格";
    _specificationActionSheet.buttons = specificationArray;
    [_specificationActionSheet show];
    
    __weak typeof(self) weakSelf = self;
    __weak BRActionSheetView *weakActionSheet = _specificationActionSheet;
    _specificationActionSheet.clickActionButtonCallBack = ^(NSInteger index) {
        [weakActionSheet close];
        
        // 更新选中的规格
        NSString *selectedSpec = [specificationArray objectAtIndex:index];
        weakSelf.usageView.selectedSpecification = selectedSpec;
        
        // 更新显示
        [weakSelf.usageView updateSpecificationDisplayText:selectedSpec];
        
        // 根据剂型调用相应的规格解析方法
        NSString *currentDrugForm = weakSelf.usageView.drugForm;
        if ([currentDrugForm isEqualToString:@"蜜丸"]) {
            [weakSelf.usageView parseSpecificationData:selectedSpec];
        } else if ([currentDrugForm isEqualToString:@"胶囊"]) {
            [weakSelf.usageView parseCapsuleSpecificationData:selectedSpec];
        } else if ([currentDrugForm isEqualToString:@"水丸"]) {
            [weakSelf.usageView parseWaterPillSpecificationData:selectedSpec];
        } else if ([currentDrugForm isEqualToString:@"膏方"]) {
            [weakSelf.usageView parseCreamFormulaSpecificationData:selectedSpec];
        }
        
        NSLog(@"选择了规格: %@，剂型: %@", selectedSpec, currentDrugForm);
    };
}

#pragma mark - 显示辅料选择弹窗
- (void)showAuxiliaryMaterialActionSheet {
    // 获取当前厂商的辅料列表
    BRSubFactoryModel *currentSubFactory = [self getCurrentSubFactoryModel];
    if (!currentSubFactory || !currentSubFactory.makeMaterialList || currentSubFactory.makeMaterialList.count == 0) {
        return;
    }
    
    // 检查当前剂型是否为膏方
    if (![_usageView.drugForm isEqualToString:@"膏方"]) {
        return;
    }
    
    [self.view endEditing:YES];
    
    // 创建辅料选择弹窗 - 使用项目标准的ActionSheet风格
    [self showAuxiliaryMaterialActionSheetWithOptions:currentSubFactory.makeMaterialList];
}

#pragma mark - 辅料选择ActionSheet弹窗
- (void)showAuxiliaryMaterialActionSheetWithOptions:(NSArray<NSString *> *)options {
    if (!options || options.count == 0) {
        return;
    }
    
    [self.view endEditing:YES];
    
    // 创建辅料选择ActionSheet，使用和规格选择相同的风格
    BRActionSheetView *auxiliaryMaterialActionSheet = [[BRActionSheetView alloc] init];
    auxiliaryMaterialActionSheet.title = @"请选择辅料";
    
    // 创建自定义视图来实现多选功能
    UIView *customView = [self createAuxiliaryMaterialCustomViewWithOptions:options];
    auxiliaryMaterialActionSheet.customView = customView;
    auxiliaryMaterialActionSheet.customHeight = customView.frame.size.height;
    
    // 添加确定按钮
    auxiliaryMaterialActionSheet.bottomTitle = @"确定";
    
    [auxiliaryMaterialActionSheet show];
    
    __weak typeof(self) weakSelf = self;
    __weak BRActionSheetView *weakActionSheet = auxiliaryMaterialActionSheet;
    
    // 确定按钮回调
    auxiliaryMaterialActionSheet.clickBottomButtonCallBack = ^{
        [weakActionSheet close];
        [weakSelf handleAuxiliaryMaterialSelection:customView withOptions:options];
    };
}

- (UIView *)createAuxiliaryMaterialCustomViewWithOptions:(NSArray<NSString *> *)options {
    // 分离辅料选项和"不添加辅料"选项
    NSMutableArray *materialOptions = [NSMutableArray array];
    BOOL hasNoMaterialOption = NO;
    
    for (NSString *option in options) {
        if ([option isEqualToString:@"不添加辅料"]) {
            hasNoMaterialOption = YES;
        } else {
            [materialOptions addObject:option];
        }
    }
    
    // 动态计算视图高度 - 使用新的按钮布局
    CGFloat totalHeight = 20; // 初始上下边距（顶部10 + 底部10）
    CGFloat contentWidth = kScreenWidth - 60; // 内容区域宽度（减去左右边距30）
    
    // 计算辅料选项区域的高度
    if (materialOptions.count > 0) {
        totalHeight += [self calculateButtonLayoutHeightForOptions:materialOptions contentWidth:contentWidth];
        totalHeight += 15; // 辅料选项区域底部间距
    }
    
    // 添加分割线和"不添加辅料"的高度
    if (hasNoMaterialOption) {
        totalHeight += 15; // 分割线上方间距
        totalHeight += 1;  // 分割线高度
        totalHeight += 15; // 分割线下方间距
        totalHeight += 40; // "不添加辅料"按钮高度
        totalHeight += 10; // "不添加辅料"按钮底部间距
    }
    
    // 先创建临时容器视图来计算实际高度
    UIView *tempContainerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, 1000)]; // 临时给个大高度
    
    // 保存按钮引用
    NSMutableArray *optionButtons = [NSMutableArray array];
    CGFloat yOffset = 10; // 顶部边距
    
    // 创建辅料选项区域（按钮样式）
    if (materialOptions.count > 0) {
        yOffset += [self createButtonLayoutForOptions:materialOptions 
                                        inContainerView:tempContainerView 
                                              yOffset:yOffset 
                                         contentWidth:contentWidth 
                                        optionButtons:optionButtons];
        yOffset += 15; // 底部间距
    }
    
    // 添加分割线和"不添加辅料"选项
    if (hasNoMaterialOption) {
        // 添加分割线上方间距
        yOffset += 15;
        
        // 分割线
        UIView *separatorLine = [[UIView alloc] initWithFrame:CGRectMake(30, yOffset, contentWidth, 1)];
        separatorLine.backgroundColor = [UIColor colorWithRed:0.9 green:0.9 blue:0.9 alpha:1.0];
        [tempContainerView addSubview:separatorLine];
        
        // 添加分割线下方间距
        yOffset += 16; // 分割线高度1 + 下方间距15
        
        // "不添加辅料"按钮 - 使用与其他按钮相同的宽度计算规则
        UIButton *noMaterialButton = [self createMaterialButtonWithOption:@"不添加辅料" 
                                                                   atIndex:materialOptions.count];
        CGFloat noMaterialButtonWidth = [self calculateButtonWidthForText:@"不添加辅料"];
        noMaterialButton.frame = CGRectMake(30, yOffset, noMaterialButtonWidth, 40);
        [tempContainerView addSubview:noMaterialButton];
        [optionButtons addObject:noMaterialButton];
        
        yOffset += 40; // 按钮高度40
    }
    
    // 添加底部边距
    yOffset += 10;
    
    // 现在创建实际的容器视图，使用计算出的实际高度
    UIView *containerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, yOffset)];
    containerView.backgroundColor = [UIColor whiteColor];
    
    // 将所有子视图从临时容器移动到实际容器
    NSArray *subviews = [tempContainerView.subviews copy];
    for (UIView *subview in subviews) {
        [subview removeFromSuperview];
        [containerView addSubview:subview];
    }
    
    // 保存引用到容器视图
    objc_setAssociatedObject(containerView, "optionButtons", optionButtons, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    objc_setAssociatedObject(containerView, "materialOptions", materialOptions, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    objc_setAssociatedObject(containerView, "hasNoMaterialOption", @(hasNoMaterialOption), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    // 恢复之前的选择状态
    [self restoreAuxiliaryMaterialSelectionStateForButtons:optionButtons 
                                            materialOptions:materialOptions 
                                        hasNoMaterialOption:hasNoMaterialOption];
    
    return containerView;
}

- (UIView *)createMaterialOptionViewWithOption:(NSString *)option 
                                       atIndex:(NSInteger)index 
                                    atPosition:(NSInteger)position 
                               checkboxButtons:(NSMutableArray *)checkboxButtons {
    
    // 计算选项视图的宽度
    CGFloat optionViewWidth = (kScreenWidth - 60) / 2; // 减去左右边距和中间间距
    
    UIView *optionView = [[UIView alloc] init];
    optionView.backgroundColor = [UIColor clearColor];
    
    // 创建复选框按钮（仅用于显示，不处理点击事件）
    UIButton *checkboxButton = [UIButton buttonWithType:UIButtonTypeCustom];
    checkboxButton.tag = index;
    [checkboxButton setTitle:@"☐" forState:UIControlStateNormal];
    [checkboxButton setTitle:@"☑" forState:UIControlStateSelected];
    [checkboxButton setTitleColor:[UIColor br_mainBlueColor] forState:UIControlStateNormal];
    [checkboxButton setTitleColor:[UIColor br_mainBlueColor] forState:UIControlStateSelected];
    checkboxButton.titleLabel.font = [UIFont systemFontOfSize:18];
    checkboxButton.userInteractionEnabled = NO; // 禁用复选框按钮的交互，由透明按钮处理
    checkboxButton.frame = CGRectMake(0, 10, 20, 20);
    [optionView addSubview:checkboxButton];
    
    // 创建文字标签，使用动态宽度
    UILabel *optionLabel = [[UILabel alloc] init];
    optionLabel.text = option;
    optionLabel.font = [UIFont systemFontOfSize:15];
    optionLabel.textColor = [UIColor blackColor];
    optionLabel.numberOfLines = 0; // 允许多行显示
    optionLabel.lineBreakMode = NSLineBreakByWordWrapping;
    
    // 计算文字标签的实际需要的尺寸
    CGFloat maxLabelWidth = optionViewWidth - 28 - 5; // 减去复选框宽度和边距
    CGSize textSize = [option boundingRectWithSize:CGSizeMake(maxLabelWidth, CGFLOAT_MAX)
                                           options:NSStringDrawingUsesLineFragmentOrigin
                                        attributes:@{NSFontAttributeName: optionLabel.font}
                                           context:nil].size;
    
    // 设置文字标签的frame，确保能完整显示
    CGFloat labelHeight = MAX(20, ceil(textSize.height)); // 最小高度20
    optionLabel.frame = CGRectMake(28, 10, maxLabelWidth, labelHeight);
    [optionView addSubview:optionLabel];
    
    // 创建透明的点击区域按钮，覆盖整个选项区域
    UIButton *tapButton = [UIButton buttonWithType:UIButtonTypeCustom];
    tapButton.backgroundColor = [UIColor clearColor]; // 完全透明背景
    tapButton.tag = index;
    [tapButton addTarget:self action:@selector(auxiliaryMaterialCheckboxTapped:) forControlEvents:UIControlEventTouchUpInside];
    tapButton.frame = CGRectMake(0, 0, optionViewWidth, MAX(40, labelHeight + 20)); // 确保点击区域足够大
    
    // 确保透明按钮在最上层
    [optionView addSubview:tapButton];
    [optionView bringSubviewToFront:tapButton];
    
    [checkboxButtons addObject:checkboxButton];
    
    return optionView;
}

// 计算选项文字需要的高度
- (CGFloat)calculateOptionHeightForText:(NSString *)text {
    CGFloat maxWidth = (kScreenWidth - 60) / 2 - 28 - 5; // 减去复选框宽度和边距
    UIFont *font = [UIFont systemFontOfSize:15];
    
    CGSize textSize = [text boundingRectWithSize:CGSizeMake(maxWidth, CGFLOAT_MAX)
                                         options:NSStringDrawingUsesLineFragmentOrigin
                                      attributes:@{NSFontAttributeName: font}
                                         context:nil].size;
    
    return ceil(textSize.height) + 20; // 加上上下边距
}

// 计算按钮布局所需的高度
- (CGFloat)calculateButtonLayoutHeightForOptions:(NSArray *)options contentWidth:(CGFloat)contentWidth {
    if (options.count == 0) {
        return 0;
    }
    
    CGFloat currentRowWidth = 0;
    NSInteger rowCount = 1; // 至少有一行
    
    for (NSString *option in options) {
        CGFloat buttonWidth = [self calculateButtonWidthForText:option];
        
        // 检查当前行是否能容纳这个按钮
        if (currentRowWidth + buttonWidth > contentWidth) {
            // 需要换行
            rowCount++;
            currentRowWidth = buttonWidth; // 重置当前行宽度
        } else {
            // 可以放在当前行
            if (currentRowWidth > 0) {
                currentRowWidth += 30; // 添加水平间距
            }
            currentRowWidth += buttonWidth;
        }
    }
    
    // 计算总高度：行数 * 按钮高度 + (行数-1) * 垂直间距
    CGFloat totalHeight = rowCount * 40 + (rowCount - 1) * 15;
    
    return totalHeight;
}

// 计算单个按钮所需的宽度
- (CGFloat)calculateButtonWidthForText:(NSString *)text {
    UIFont *font = [UIFont systemFontOfSize:15];
    CGSize textSize = [text boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, 40)
                                         options:NSStringDrawingUsesLineFragmentOrigin
                                      attributes:@{NSFontAttributeName: font}
                                         context:nil].size;
    
    return ceil(textSize.width) + 30; // 文字宽度 + 左右内边距15*2
}

// 创建按钮布局
- (CGFloat)createButtonLayoutForOptions:(NSArray *)options 
                        inContainerView:(UIView *)containerView 
                                yOffset:(CGFloat)startY 
                           contentWidth:(CGFloat)contentWidth 
                          optionButtons:(NSMutableArray *)optionButtons {
    
    CGFloat currentX = 30; // 左边距
    CGFloat currentY = startY;
    
    for (NSInteger i = 0; i < options.count; i++) {
        NSString *option = options[i];
        CGFloat buttonWidth = [self calculateButtonWidthForText:option];
        
        // 检查当前行是否能容纳这个按钮
        if (currentX + buttonWidth > kScreenWidth - 30) { // 右边距30
            // 需要换行
            currentX = 30; // 重置到左边距
            currentY += 55; // 按钮高度40 + 垂直间距15
        }
        
        // 创建按钮
        UIButton *optionButton = [self createMaterialButtonWithOption:option atIndex:i];
        optionButton.frame = CGRectMake(currentX, currentY, buttonWidth, 40);
        [containerView addSubview:optionButton];
        [optionButtons addObject:optionButton];
        
        // 更新下一个按钮的x坐标
        currentX += buttonWidth + 30; // 按钮宽度 + 水平间距30
    }
    
    return currentY + 40 - startY; // 返回总高度
}

// 创建辅料按钮
- (UIButton *)createMaterialButtonWithOption:(NSString *)option atIndex:(NSInteger)index {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.tag = index;
    [button setTitle:option forState:UIControlStateNormal];
    button.titleLabel.font = [UIFont systemFontOfSize:15];
    
    // 未选中状态：黑色文字，灰色边框，白色背景
    [button setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [button setBackgroundColor:[UIColor whiteColor]];
    button.layer.borderWidth = 1.0;
    button.layer.borderColor = [UIColor lightGrayColor].CGColor;
    button.layer.cornerRadius = 12;
    
    // 选中状态：白色文字，蓝色背景
    [button setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
    
    // 设置内边距
    button.contentEdgeInsets = UIEdgeInsetsMake(0, 15, 0, 15);
    
    // 添加点击事件
    [button addTarget:self action:@selector(auxiliaryMaterialButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    
    return button;
}

- (void)restoreAuxiliaryMaterialSelectionState:(NSArray *)checkboxButtons 
                               materialOptions:(NSArray *)materialOptions 
                           hasNoMaterialOption:(BOOL)hasNoMaterialOption {
    
    if (_usageView.isNoAuxiliaryMaterial && hasNoMaterialOption) {
        // 选中"不添加辅料"
        UIButton *noMaterialCheckbox = [checkboxButtons lastObject];
        noMaterialCheckbox.selected = YES;
    } else {
        // 恢复之前选择的辅料
        for (NSString *selectedMaterial in _usageView.selectedAuxiliaryMaterials) {
            NSInteger index = [materialOptions indexOfObject:selectedMaterial];
            if (index != NSNotFound && index < checkboxButtons.count) {
                UIButton *checkbox = checkboxButtons[index];
                checkbox.selected = YES;
            }
        }
    }
}

// 新的按钮状态恢复方法
- (void)restoreAuxiliaryMaterialSelectionStateForButtons:(NSArray *)optionButtons 
                                         materialOptions:(NSArray *)materialOptions 
                                     hasNoMaterialOption:(BOOL)hasNoMaterialOption {
    
    if (_usageView.isNoAuxiliaryMaterial && hasNoMaterialOption) {
        // 选中"不添加辅料"
        UIButton *noMaterialButton = [optionButtons lastObject];
        [self updateButtonSelectedState:noMaterialButton selected:YES];
    } else {
        // 恢复之前选择的辅料
        for (NSString *selectedMaterial in _usageView.selectedAuxiliaryMaterials) {
            NSInteger index = [materialOptions indexOfObject:selectedMaterial];
            if (index != NSNotFound && index < optionButtons.count) {
                UIButton *button = optionButtons[index];
                [self updateButtonSelectedState:button selected:YES];
            }
        }
    }
}

// 更新按钮选中状态的样式
- (void)updateButtonSelectedState:(UIButton *)button selected:(BOOL)selected {
    button.selected = selected;
    
    if (selected) {
        // 选中状态：蓝色背景，白色文字，无边框
        [button setBackgroundColor:[UIColor br_mainBlueColor]];
        button.layer.borderWidth = 0;
    } else {
        // 未选中状态：白色背景，黑色文字，灰色边框
        [button setBackgroundColor:[UIColor whiteColor]];
        button.layer.borderWidth = 1.0;
        button.layer.borderColor = [UIColor lightGrayColor].CGColor;
    }
}

- (void)handleAuxiliaryMaterialSelection:(UIView *)customView withOptions:(NSArray *)options {
    NSArray *optionButtons = objc_getAssociatedObject(customView, "optionButtons");
    NSArray *materialOptions = objc_getAssociatedObject(customView, "materialOptions");
    NSNumber *hasNoMaterialOptionNumber = objc_getAssociatedObject(customView, "hasNoMaterialOption");
    BOOL hasNoMaterialOption = [hasNoMaterialOptionNumber boolValue];
    
    NSMutableArray *selectedMaterials = [NSMutableArray array];
    BOOL selectedNoMaterial = NO;
    
    // 收集选择结果
    for (NSInteger i = 0; i < optionButtons.count; i++) {
        UIButton *button = optionButtons[i];
        if (button.selected) {
            if (i < materialOptions.count) {
                // 普通辅料选项
                [selectedMaterials addObject:materialOptions[i]];
            } else if (hasNoMaterialOption && i == materialOptions.count) {
                // "不添加辅料"选项
                selectedNoMaterial = YES;
            }
        }
    }
    
    // 更新选择状态
    if (selectedNoMaterial) {
        // 选择了"不添加辅料"，清空其他选择
        _usageView.isNoAuxiliaryMaterial = YES;
        [_usageView.selectedAuxiliaryMaterials removeAllObjects];
        [_usageView updateAuxiliaryMaterialDisplayText:@"不添加辅料"];
    } else {
        // 选择了具体的辅料
        _usageView.isNoAuxiliaryMaterial = NO;
        [_usageView.selectedAuxiliaryMaterials removeAllObjects];
        [_usageView.selectedAuxiliaryMaterials addObjectsFromArray:selectedMaterials];
        
        if (selectedMaterials.count > 0) {
            NSString *displayText = [selectedMaterials componentsJoinedByString:@","];
            [_usageView updateAuxiliaryMaterialDisplayText:displayText];
        } else {
            [_usageView updateAuxiliaryMaterialDisplayText:@"请选择辅料"];
        }
    }
    
    NSLog(@"选择了辅料: %@，不添加辅料: %@", selectedMaterials, selectedNoMaterial ? @"是" : @"否");
}

#pragma mark - 辅料选择交互处理

// 新的按钮点击处理方法
- (void)auxiliaryMaterialButtonTapped:(UIButton *)sender {
    NSLog(@"辅料按钮被点击，tag: %ld", (long)sender.tag);
    
    // 找到容器视图和相关数据（按钮现在直接添加到容器视图中）
    UIView *containerView = sender.superview;
    
    NSArray *optionButtons = objc_getAssociatedObject(containerView, "optionButtons");
    NSArray *materialOptions = objc_getAssociatedObject(containerView, "materialOptions");
    NSNumber *hasNoMaterialOptionNumber = objc_getAssociatedObject(containerView, "hasNoMaterialOption");
    BOOL hasNoMaterialOption = [hasNoMaterialOptionNumber boolValue];
    
    // 切换当前按钮状态
    BOOL newSelectedState = !sender.selected;
    [self updateButtonSelectedState:sender selected:newSelectedState];
    
    // 判断是否选择了"不添加辅料"
    BOOL isNoMaterialOption = hasNoMaterialOption && sender.tag == materialOptions.count;
    
    if (newSelectedState && isNoMaterialOption) {
        // 如果选择了"不添加辅料"，取消其他所有选择
        for (UIButton *button in optionButtons) {
            if (button != sender) {
                [self updateButtonSelectedState:button selected:NO];
            }
        }
    } else if (newSelectedState && !isNoMaterialOption) {
        // 如果选择了其他辅料，取消"不添加辅料"的选择
        if (hasNoMaterialOption && optionButtons.count > materialOptions.count) {
            UIButton *noMaterialButton = [optionButtons lastObject];
            [self updateButtonSelectedState:noMaterialButton selected:NO];
        }
    }
}

- (void)auxiliaryMaterialCheckboxTapped:(UIButton *)sender {
    NSLog(@"辅料选项被点击，tag: %ld", (long)sender.tag);
    
    // 找到对应的复选框按钮并切换状态
    UIView *optionView = sender.superview; // optionView
    UIScrollView *scrollView = (UIScrollView *)optionView.superview; // scrollView
    UIView *containerView = scrollView.superview; // containerView
    
    NSLog(@"视图层级 - optionView: %@, scrollView: %@, containerView: %@", 
          optionView, scrollView, containerView);
    
    NSArray *checkboxButtons = objc_getAssociatedObject(containerView, "checkboxButtons");
    NSArray *materialOptions = objc_getAssociatedObject(containerView, "materialOptions");
    NSNumber *hasNoMaterialOptionNumber = objc_getAssociatedObject(containerView, "hasNoMaterialOption");
    BOOL hasNoMaterialOption = [hasNoMaterialOptionNumber boolValue];
    
    // 找到对应的复选框按钮并切换其状态
    UIButton *checkboxButton = nil;
    if (sender.tag < checkboxButtons.count) {
        checkboxButton = checkboxButtons[sender.tag];
        checkboxButton.selected = !checkboxButton.selected;
    }
    
    // 判断是否选择了"不添加辅料"
    BOOL isNoMaterialOption = hasNoMaterialOption && sender.tag == materialOptions.count;
    
    if (checkboxButton.selected && isNoMaterialOption) {
        // 如果选择了"不添加辅料"，取消其他所有选择
        for (UIButton *checkbox in checkboxButtons) {
            if (checkbox != checkboxButton) {
                checkbox.selected = NO;
            }
        }
    } else if (checkboxButton.selected && !isNoMaterialOption) {
        // 如果选择了其他辅料，取消"不添加辅料"的选择
        if (hasNoMaterialOption && checkboxButtons.count > materialOptions.count) {
            UIButton *noMaterialCheckbox = [checkboxButtons lastObject];
            noMaterialCheckbox.selected = NO;
        }
    }
}





@end
